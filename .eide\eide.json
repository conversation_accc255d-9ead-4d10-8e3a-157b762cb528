{"name": "ctrl_c_project", "type": "ARM", "dependenceList": [], "srcDirs": [], "virtualFolder": {"name": "<virtual_root>", "files": [], "folders": [{"name": "Application", "files": [], "folders": [{"name": "MDK-ARM", "files": [{"path": "startup_stm32f407xx.s"}], "folders": []}, {"name": "User", "files": [], "folders": [{"name": "Core", "files": [{"path": "../Core/Src/main.c"}, {"path": "../Core/Src/gpio.c"}, {"path": "../Core/Src/dma.c"}, {"path": "../Core/Src/i2c.c"}, {"path": "../Core/Src/tim.c"}, {"path": "../Core/Src/usart.c"}, {"path": "../Core/Src/stm32f4xx_it.c"}, {"path": "../Core/Src/stm32f4xx_hal_msp.c"}], "folders": []}]}]}, {"name": "Drivers", "files": [], "folders": [{"name": "STM32F4xx_HAL_Driver", "files": [{"path": "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_i2c.c"}, {"path": "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_i2c_ex.c"}, {"path": "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.c"}, {"path": "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.c"}, {"path": "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.c"}, {"path": "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.c"}, {"path": "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.c"}, {"path": "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.c"}, {"path": "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.c"}, {"path": "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.c"}, {"path": "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.c"}, {"path": "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.c"}, {"path": "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.c"}, {"path": "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.c"}, {"path": "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.c"}, {"path": "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.c"}, {"path": "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.c"}, {"path": "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_uart.c"}], "folders": []}, {"name": "CMSIS", "files": [{"path": "../Core/Src/system_stm32f4xx.c"}], "folders": []}]}, {"name": "User", "files": [{"path": "../User/my_scheduler.c"}, {"path": "../User/my_scheduler.h"}, {"path": "../User/mydefine.h"}, {"path": "../User/my_timer.c"}, {"path": "../User/my_timer.h"}], "folders": [{"name": "<PERSON><PERSON><PERSON>", "files": [], "folders": [{"name": "ringbuffer", "files": [{"path": "../User/Module/ringbuffer/readme.txt"}, {"path": "../User/Module/ringbuffer/ringbuffer.c"}, {"path": "../User/Module/ringbuffer/ringbuffer.h"}], "folders": []}, {"name": "grayscale", "files": [{"path": "../User/Module/grayscale/gw_grayscale_sensor.h"}, {"path": "../User/Module/grayscale/hardware_iic.c"}, {"path": "../User/Module/grayscale/hardware_iic.h"}, {"path": "../User/Module/grayscale/software_iic.c"}, {"path": "../User/Module/grayscale/software_iic.h"}], "folders": []}, {"name": "pid", "files": [{"path": "../User/Module/pid/pid.c"}, {"path": "../User/Module/pid/pid.h"}], "folders": []}, {"name": "motor", "files": [{"path": "../User/Module/motor/motor_driver.c"}, {"path": "../User/Module/motor/motor_driver.h"}], "folders": []}, {"name": "oled", "files": [{"path": "../User/Module/oled/oled.c"}, {"path": "../User/Module/oled/oled.h"}, {"path": "../User/Module/oled/oledfont.h"}, {"path": "../User/Module/oled/oledpic.h"}], "folders": []}, {"name": "encoder", "files": [{"path": "../User/Module/encoder/encoder_driver.c"}, {"path": "../User/Module/encoder/encoder_driver.h"}], "folders": []}, {"name": "bno08x", "files": [{"path": "../User/Module/bno08x/readme.txt"}, {"path": "../User/Module/bno08x/bno08x_hal.c"}, {"path": "../User/Module/bno08x/bno08x_hal.h"}], "folders": []}]}, {"name": "Driver", "files": [{"path": "../User/Driver/led_driver.c"}, {"path": "../User/Driver/led_driver.h"}, {"path": "../User/Driver/key_driver.c"}, {"path": "../User/Driver/key_driver.h"}], "folders": []}, {"name": "App", "files": [{"path": "../User/App/usart_app.c"}, {"path": "../User/App/usart_app.h"}, {"path": "../User/App/led_app.c"}, {"path": "../User/App/led_app.h"}, {"path": "../User/App/key_app.c"}, {"path": "../User/App/key_app.h"}, {"path": "../User/App/oled_app.c"}, {"path": "../User/App/oled_app.h"}, {"path": "../User/App/motor_app.c"}, {"path": "../User/App/motor_app.h"}, {"path": "../User/App/encoder_app.c"}, {"path": "../User/App/encoder_app.h"}, {"path": "../User/App/pid_app.c"}, {"path": "../User/App/pid_app.h"}, {"path": "../User/App/gray_app.c"}, {"path": "../User/App/gray_app.h"}, {"path": "../User/App/bno08x_app.c"}, {"path": "../User/App/bno08x_app.h"}], "folders": []}]}]}, "outDir": "build", "deviceName": null, "packDir": null, "miscInfo": {"uid": "5ffdf11ab318a9b6ceb485640b2b0c4a"}, "targets": {"ctrl_c_project": {"excludeList": [], "toolchain": "AC5", "compileConfig": {"cpuType": "Cortex-M4", "archExtensions": "", "floatingPointHardware": "single", "scatterFilePath": "", "useCustomScatterFile": false, "storageLayout": {"RAM": [{"tag": "RAM", "id": 1, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "noInit": false}, {"tag": "RAM", "id": 2, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "noInit": false}, {"tag": "RAM", "id": 3, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "noInit": false}, {"tag": "IRAM", "id": 1, "mem": {"startAddr": "0x20000000", "size": "0x1c000"}, "isChecked": true, "noInit": false}, {"tag": "IRAM", "id": 2, "mem": {"startAddr": "0x2001c000", "size": "0x4000"}, "isChecked": true, "noInit": false}], "ROM": [{"tag": "ROM", "id": 1, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "isStartup": false}, {"tag": "ROM", "id": 2, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "isStartup": false}, {"tag": "ROM", "id": 3, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "isStartup": false}, {"tag": "IROM", "id": 1, "mem": {"startAddr": "0x8000000", "size": "0x80000"}, "isChecked": true, "isStartup": true}, {"tag": "IROM", "id": 2, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "isStartup": false}]}, "options": "null"}, "uploader": "JLink", "uploadConfig": {"bin": "", "baseAddr": "", "cpuInfo": {"vendor": "null", "cpuName": "null"}, "proType": 1, "speed": 8000, "otherCmds": ""}, "uploadConfigMap": {}, "custom_dep": {"name": "default", "incList": ["../Core/Inc", "../Drivers/STM32F4xx_HAL_Driver/Inc", "../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy", "../Drivers/CMSIS/Device/ST/STM32F4xx/Include", "../Drivers/CMSIS/Include", "../User", "../User/App", "../User/Driver", "../User/Module/bno08x", "../User/Module/grayscale", "../User/Module/motor", "../User/Module/pid", "../User/Module/ringbuffer", "../User/Module/jy901s", "../User/Module/oled", "../User/Module/encoder", ".cmsis/include", "RTE/_ctrl_c_project"], "libList": [], "defineList": ["USE_HAL_DRIVER", "STM32F407xx"]}, "builderOptions": {"AC5": {"version": 4, "beforeBuildTasks": [], "afterBuildTasks": [], "global": {"use-microLIB": false, "output-debug-info": "enable"}, "c/cpp-compiler": {"optimization": "level-0", "one-elf-section-per-function": true, "c99-mode": true, "C_FLAGS": "--diag_suppress=1 --diag_suppress=1295", "CXX_FLAGS": "--diag_suppress=1 --diag_suppress=1295", "warnings": "all-warnings"}, "asm-compiler": {}, "linker": {"output-format": "elf", "xo-base": "", "ro-base": "", "rw-base": ""}}}}}, "version": "3.6"}