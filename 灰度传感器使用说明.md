# 灰度传感器使用说明文档

## 1. 概述

本工程使用感为智能科技的8路灰度传感器，通过软件I2C进行通信，主要用于循线控制功能。传感器能够检测黑线位置并计算偏差值，为PID控制器提供反馈信号。

## 2. 硬件配置

### 2.1 传感器规格
- **型号**: 感为智能8路灰度传感器
- **通信方式**: I2C协议
- **I2C地址**: 0x4C (默认地址)
- **检测通道**: 8路数字量输出
- **供电电压**: 3.3V/5V

### 2.2 引脚连接
```
STM32F407VET6    ←→    灰度传感器
PC4 (SCL)        ←→    SCL
PC5 (SDA)        ←→    SDA
VCC              ←→    VCC (3.3V)
GND              ←→    GND
```

### 2.3 GPIO配置
在STM32CubeMX中配置：
- **PC4**: GPIO_Output (开漏输出，上拉)
- **PC5**: GPIO_Output (开漏输出，上拉)
- **引脚标签**: 
  - PC4: `GRAY_SOFT_SCL`
  - PC5: `GRAY_SOFT_SDA`

## 3. 软件架构

### 3.1 文件结构
```
User/
├── App/
│   ├── gray_app.h          # 灰度传感器应用层头文件
│   └── gray_app.c          # 灰度传感器应用层实现
└── Module/
    └── grayscale/
        ├── gw_grayscale_sensor.h    # 传感器寄存器定义
        ├── software_iic.h           # 软件I2C头文件
        ├── software_iic.c           # 软件I2C实现
        ├── hardware_iic.h           # 硬件I2C头文件(备用)
        └── hardware_iic.c           # 硬件I2C实现(备用)
```

### 3.2 软件I2C实现
采用GPIO模拟I2C时序，具有以下特点：
- **时钟频率**: 约50kHz (10μs延时)
- **兼容性**: 标准I2C协议
- **可靠性**: 包含错误检测和重试机制

## 4. API接口说明

### 4.1 初始化函数
```c
void Gray_Init(void);
```
- **功能**: 初始化灰度传感器，检测连接状态
- **返回值**: 无
- **说明**: 通过Ping命令检测传感器是否正常连接

### 4.2 数据读取函数
```c
void Gray_Task(void);
```
- **功能**: 读取8路灰度传感器数据并计算循线偏差
- **调用周期**: 建议5ms
- **输出**: 更新全局变量 `g_line_position_error`

### 4.3 底层I2C函数
```c
unsigned char Ping(void);                    // 传感器连接检测
unsigned char IIC_Get_Digtal(void);          // 获取8位数字量数据
unsigned char IIC_Get_Anolog(unsigned char *Result, unsigned char len);  // 获取模拟量数据
unsigned char IIC_Get_Single_Anolog(unsigned char Channel);  // 获取单通道模拟量
```

## 5. 数据处理算法

### 5.1 权重分配
8路传感器的权重分配如下：
```c
float gray_weights[8] = {-4.0f, -3.0f, -2.0f, -1.0f, 1.0f, 2.0f, 3.0f, 4.0f};
```

### 5.2 偏差计算
```c
// 加权平均算法
float weighted_sum = 0;
uint8_t black_line_count = 0;

for(uint8_t i = 0; i < 8; i++) {
    if((Digtal>>i) & 0x01) {  // 检测到黑线
        weighted_sum += gray_weights[i];
        black_line_count++;
    }
}

if(black_line_count > 0)
    g_line_position_error = weighted_sum / (float)black_line_count;
```

### 5.3 偏差值含义
- **负值**: 黑线偏向左侧 (范围: -4.0 ~ -1.0)
- **正值**: 黑线偏向右侧 (范围: 1.0 ~ 4.0)
- **0值**: 黑线居中或未检测到黑线

## 6. 使用方法

### 6.1 启用灰度传感器
在 `my_scheduler.c` 中取消注释：
```c
task all_task[]={
    {led_task,1,0},
    {Gray_Task,5,0},        // 取消注释这一行
    {uart_task,5,0},
    {key_task,10,0},
    {oled_task,100,0},
};
```

### 6.2 在PID控制中使用
```c
void Line_PID_control(void) {
    // 使用灰度传感器偏差值作为PID输入
    int line_pid_output = pid_calculate_positional(&pid_line, g_line_position_error);
    
    // 输出限幅
    line_pid_output = pid_constrain(line_pid_output, pid_params_line.out_min, pid_params_line.out_max);
    
    // 分配给左右电机
    // ...
}
```

### 6.3 调试输出
```c
// 在Gray_Task()中添加调试信息
my_printf(&huart1, "Digital: %d-%d-%d-%d-%d-%d-%d-%d, Error: %.2f\r\n",
    (Digtal>>0)&0x01, (Digtal>>1)&0x01, (Digtal>>2)&0x01, (Digtal>>3)&0x01,
    (Digtal>>4)&0x01, (Digtal>>5)&0x01, (Digtal>>6)&0x01, (Digtal>>7)&0x01,
    g_line_position_error);
```

## 7. 故障排除

### 7.1 常见问题
1. **传感器连接失败**
   - 检查I2C引脚连接
   - 确认供电电压正常
   - 检查I2C地址是否正确

2. **数据读取异常**
   - 检查软件I2C时序
   - 确认传感器固件版本
   - 检查电磁干扰

3. **循线效果不佳**
   - 调整传感器高度 (建议2-5mm)
   - 校准黑白阈值
   - 优化PID参数

### 7.2 调试命令
```c
// 检测传感器连接
if(Ping() == 0) {
    printf("传感器连接正常\n");
} else {
    printf("传感器连接失败\n");
}

// 读取原始数据
uint8_t digital_data = IIC_Get_Digtal();
printf("原始数据: 0x%02X\n", digital_data);
```

## 8. 性能优化

### 8.1 采样频率优化
- **推荐频率**: 200Hz (5ms周期)
- **最高频率**: 500Hz (2ms周期)
- **平衡考虑**: 响应速度 vs CPU占用率

### 8.2 滤波处理
```c
// 简单滑动平均滤波
#define FILTER_SIZE 3
static float error_buffer[FILTER_SIZE] = {0};
static uint8_t filter_index = 0;

// 添加新数据
error_buffer[filter_index] = g_line_position_error;
filter_index = (filter_index + 1) % FILTER_SIZE;

// 计算平均值
float filtered_error = 0;
for(int i = 0; i < FILTER_SIZE; i++) {
    filtered_error += error_buffer[i];
}
filtered_error /= FILTER_SIZE;
```

## 9. 扩展功能

### 9.1 模拟量读取
```c
// 读取所有通道模拟量
uint8_t analog_data[8];
if(IIC_Get_Anolog(analog_data, 8)) {
    // 处理模拟量数据
    for(int i = 0; i < 8; i++) {
        printf("通道%d: %d\n", i+1, analog_data[i]);
    }
}
```

### 9.2 传感器校准
```c
// 黑线校准
IIC_WriteByte((GW_GRAY_ADDR_DEF<<1), GW_GRAY_CALIBRATION_BLACK, 0x01);

// 白线校准  
IIC_WriteByte((GW_GRAY_ADDR_DEF<<1), GW_GRAY_CALIBRATION_WHITE, 0x01);
```

## 10. 注意事项

1. **硬件注意事项**
   - 确保I2C上拉电阻正确 (4.7kΩ)
   - 避免长线连接，减少干扰
   - 传感器安装要稳固，避免震动

2. **软件注意事项**
   - 软件I2C不支持中断，会占用CPU时间
   - 在高频任务中使用时注意时序
   - 定期检查传感器连接状态

3. **调试建议**
   - 先确保硬件连接正常
   - 逐步验证各个功能模块
   - 使用示波器检查I2C时序

## 11. 版本历史

- **v1.0** (2025-01-27): 初始版本，支持基本循线功能
- 支持8路数字量读取
- 实现软件I2C通信
- 集成PID循线控制

---

**作者**: AI Assistant  
**日期**: 2025-01-27  
**版本**: v1.0
