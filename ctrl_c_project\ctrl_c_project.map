Component: ARM Compiler 5.06 update 7 (build 960) Tool: armlink [4d3601]

==============================================================================

Section Cross References

    startup_stm32f407xx.o(STACK) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f407xx.o(HEAP) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f407xx.o(RESET) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f407xx.o(RESET) refers to startup_stm32f407xx.o(STACK) for __initial_sp
    startup_stm32f407xx.o(RESET) refers to startup_stm32f407xx.o(.text) for Reset_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.NMI_Handler) for NMI_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.HardFault_Handler) for HardFault_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.MemManage_Handler) for MemManage_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.BusFault_Handler) for BusFault_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.UsageFault_Handler) for UsageFault_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.SVC_Handler) for SVC_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.DebugMon_Handler) for DebugMon_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.PendSV_Handler) for PendSV_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.SysTick_Handler) for SysTick_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.EXTI3_IRQHandler) for EXTI3_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.DMA1_Stream0_IRQHandler) for DMA1_Stream0_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.TIM2_IRQHandler) for TIM2_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.USART1_IRQHandler) for USART1_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.UART5_IRQHandler) for UART5_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.DMA2_Stream2_IRQHandler) for DMA2_Stream2_IRQHandler
    startup_stm32f407xx.o(.text) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f407xx.o(.text) refers to system_stm32f4xx.o(i.SystemInit) for SystemInit
    startup_stm32f407xx.o(.text) refers to __main.o(!!!main) for __main
    startup_stm32f407xx.o(.text) refers to startup_stm32f407xx.o(HEAP) for Heap_Mem
    startup_stm32f407xx.o(.text) refers to startup_stm32f407xx.o(STACK) for Stack_Mem
    main.o(i.SystemClock_Config) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    main.o(i.SystemClock_Config) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig) for HAL_RCC_OscConfig
    main.o(i.SystemClock_Config) refers to main.o(i.Error_Handler) for Error_Handler
    main.o(i.SystemClock_Config) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) for HAL_RCC_ClockConfig
    main.o(i.main) refers to stm32f4xx_hal.o(i.HAL_Init) for HAL_Init
    main.o(i.main) refers to main.o(i.SystemClock_Config) for SystemClock_Config
    main.o(i.main) refers to gpio.o(i.MX_GPIO_Init) for MX_GPIO_Init
    main.o(i.main) refers to dma.o(i.MX_DMA_Init) for MX_DMA_Init
    main.o(i.main) refers to usart.o(i.MX_USART1_UART_Init) for MX_USART1_UART_Init
    main.o(i.main) refers to i2c.o(i.MX_I2C1_Init) for MX_I2C1_Init
    main.o(i.main) refers to tim.o(i.MX_TIM3_Init) for MX_TIM3_Init
    main.o(i.main) refers to tim.o(i.MX_TIM4_Init) for MX_TIM4_Init
    main.o(i.main) refers to i2c.o(i.MX_I2C2_Init) for MX_I2C2_Init
    main.o(i.main) refers to tim.o(i.MX_TIM1_Init) for MX_TIM1_Init
    main.o(i.main) refers to usart.o(i.MX_UART5_Init) for MX_UART5_Init
    main.o(i.main) refers to tim.o(i.MX_TIM2_Init) for MX_TIM2_Init
    main.o(i.main) refers to my_scheduler.o(i.all_task_init) for all_task_init
    main.o(i.main) refers to my_scheduler.o(i.all_task_run) for all_task_run
    gpio.o(i.MX_GPIO_Init) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    gpio.o(i.MX_GPIO_Init) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    gpio.o(i.MX_GPIO_Init) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    gpio.o(i.MX_GPIO_Init) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    gpio.o(i.MX_GPIO_Init) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    dma.o(i.MX_DMA_Init) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    dma.o(i.MX_DMA_Init) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    i2c.o(i.HAL_I2C_MspDeInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    i2c.o(i.HAL_I2C_MspInit) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    i2c.o(i.HAL_I2C_MspInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    i2c.o(i.MX_I2C1_Init) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_Init) for HAL_I2C_Init
    i2c.o(i.MX_I2C1_Init) refers to main.o(i.Error_Handler) for Error_Handler
    i2c.o(i.MX_I2C1_Init) refers to i2c.o(.bss) for hi2c1
    i2c.o(i.MX_I2C2_Init) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_Init) for HAL_I2C_Init
    i2c.o(i.MX_I2C2_Init) refers to main.o(i.Error_Handler) for Error_Handler
    i2c.o(i.MX_I2C2_Init) refers to i2c.o(.bss) for hi2c2
    tim.o(i.HAL_TIM_Base_MspDeInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ) for HAL_NVIC_DisableIRQ
    tim.o(i.HAL_TIM_Base_MspInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    tim.o(i.HAL_TIM_Base_MspInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    tim.o(i.HAL_TIM_Encoder_MspDeInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    tim.o(i.HAL_TIM_Encoder_MspInit) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    tim.o(i.HAL_TIM_Encoder_MspInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    tim.o(i.HAL_TIM_MspPostInit) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    tim.o(i.HAL_TIM_MspPostInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    tim.o(i.MX_TIM1_Init) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    tim.o(i.MX_TIM1_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init) for HAL_TIM_Base_Init
    tim.o(i.MX_TIM1_Init) refers to main.o(i.Error_Handler) for Error_Handler
    tim.o(i.MX_TIM1_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) for HAL_TIM_ConfigClockSource
    tim.o(i.MX_TIM1_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Init) for HAL_TIM_PWM_Init
    tim.o(i.MX_TIM1_Init) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(i.MX_TIM1_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) for HAL_TIM_PWM_ConfigChannel
    tim.o(i.MX_TIM1_Init) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigBreakDeadTime) for HAL_TIMEx_ConfigBreakDeadTime
    tim.o(i.MX_TIM1_Init) refers to tim.o(i.HAL_TIM_MspPostInit) for HAL_TIM_MspPostInit
    tim.o(i.MX_TIM1_Init) refers to tim.o(.bss) for htim1
    tim.o(i.MX_TIM2_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init) for HAL_TIM_Base_Init
    tim.o(i.MX_TIM2_Init) refers to main.o(i.Error_Handler) for Error_Handler
    tim.o(i.MX_TIM2_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) for HAL_TIM_ConfigClockSource
    tim.o(i.MX_TIM2_Init) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(i.MX_TIM2_Init) refers to tim.o(.bss) for htim2
    tim.o(i.MX_TIM3_Init) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    tim.o(i.MX_TIM3_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Init) for HAL_TIM_Encoder_Init
    tim.o(i.MX_TIM3_Init) refers to main.o(i.Error_Handler) for Error_Handler
    tim.o(i.MX_TIM3_Init) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(i.MX_TIM3_Init) refers to tim.o(.bss) for htim3
    tim.o(i.MX_TIM4_Init) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    tim.o(i.MX_TIM4_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Init) for HAL_TIM_Encoder_Init
    tim.o(i.MX_TIM4_Init) refers to main.o(i.Error_Handler) for Error_Handler
    tim.o(i.MX_TIM4_Init) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(i.MX_TIM4_Init) refers to tim.o(.bss) for htim4
    usart.o(i.HAL_UART_MspDeInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    usart.o(i.HAL_UART_MspDeInit) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_DeInit) for HAL_DMA_DeInit
    usart.o(i.HAL_UART_MspDeInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ) for HAL_NVIC_DisableIRQ
    usart.o(i.HAL_UART_MspInit) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    usart.o(i.HAL_UART_MspInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    usart.o(i.HAL_UART_MspInit) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Init) for HAL_DMA_Init
    usart.o(i.HAL_UART_MspInit) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.HAL_UART_MspInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    usart.o(i.HAL_UART_MspInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    usart.o(i.HAL_UART_MspInit) refers to usart.o(.bss) for hdma_uart5_rx
    usart.o(i.MX_UART5_Init) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    usart.o(i.MX_UART5_Init) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.MX_UART5_Init) refers to usart.o(.bss) for huart5
    usart.o(i.MX_USART1_UART_Init) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    usart.o(i.MX_USART1_UART_Init) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.MX_USART1_UART_Init) refers to usart.o(.bss) for huart1
    stm32f4xx_it.o(i.DMA1_Stream0_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler) for HAL_DMA_IRQHandler
    stm32f4xx_it.o(i.DMA1_Stream0_IRQHandler) refers to usart.o(.bss) for hdma_uart5_rx
    stm32f4xx_it.o(i.DMA2_Stream2_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler) for HAL_DMA_IRQHandler
    stm32f4xx_it.o(i.DMA2_Stream2_IRQHandler) refers to usart.o(.bss) for hdma_usart1_rx
    stm32f4xx_it.o(i.EXTI3_IRQHandler) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler) for HAL_GPIO_EXTI_IRQHandler
    stm32f4xx_it.o(i.SysTick_Handler) refers to stm32f4xx_hal.o(i.HAL_IncTick) for HAL_IncTick
    stm32f4xx_it.o(i.TIM2_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) for HAL_TIM_IRQHandler
    stm32f4xx_it.o(i.TIM2_IRQHandler) refers to tim.o(.bss) for htim2
    stm32f4xx_it.o(i.UART5_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32f4xx_it.o(i.UART5_IRQHandler) refers to usart.o(.bss) for huart5
    stm32f4xx_it.o(i.USART1_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32f4xx_it.o(i.USART1_IRQHandler) refers to usart.o(.bss) for huart1
    stm32f4xx_hal_msp.o(i.HAL_MspInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping) for HAL_NVIC_SetPriorityGrouping
    stm32f4xx_hal_i2c.o(i.HAL_I2C_DeInit) refers to i2c.o(i.HAL_I2C_MspDeInit) for HAL_I2C_MspDeInit
    stm32f4xx_hal_i2c.o(i.HAL_I2C_ER_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_Slave_AF) for I2C_Slave_AF
    stm32f4xx_hal_i2c.o(i.HAL_I2C_ER_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_ConvertOtherXferOptions) for I2C_ConvertOtherXferOptions
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_Master_SB) for I2C_Master_SB
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_Master_ADD10) for I2C_Master_ADD10
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_Master_ADDR) for I2C_Master_ADDR
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_TXE) for I2C_MasterTransmit_TXE
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_BTF) for I2C_MasterTransmit_BTF
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_MemoryTransmit_TXE_BTF) for I2C_MemoryTransmit_TXE_BTF
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_RXNE) for I2C_MasterReceive_RXNE
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_BTF) for I2C_MasterReceive_BTF
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_Slave_ADDR) for I2C_Slave_ADDR
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_Slave_STOPF) for I2C_Slave_STOPF
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_SlaveTransmit_TXE) for I2C_SlaveTransmit_TXE
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_SlaveTransmit_BTF) for I2C_SlaveTransmit_BTF
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_SlaveReceive_RXNE) for I2C_SlaveReceive_RXNE
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_SlaveReceive_BTF) for I2C_SlaveReceive_BTF
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Init) refers to i2c.o(i.HAL_I2C_MspInit) for HAL_I2C_MspInit
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Init) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32f4xx_hal_i2c.o(i.HAL_I2C_IsDeviceReady) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_IsDeviceReady) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Abort_IT) refers to stm32f4xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32f4xx_hal_i2c.o(i.I2C_MasterRequestRead) for I2C_MasterRequestRead
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) for I2C_WaitOnRXNEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_IT) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_IT) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_IT) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32f4xx_hal_i2c.o(i.I2C_MasterRequestWrite) for I2C_MasterRequestWrite
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout) for I2C_WaitOnBTFFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_IT) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryRead) for I2C_RequestMemoryRead
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) for I2C_WaitOnRXNEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryRead) for I2C_RequestMemoryRead
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_IT) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryWrite) for I2C_RequestMemoryWrite
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout) for I2C_WaitOnBTFFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryWrite) for I2C_RequestMemoryWrite
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_IT) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) for I2C_WaitOnRXNEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout) for I2C_WaitOnSTOPFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.I2C_DMAAbort) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_AbortCpltCallback) for HAL_I2C_AbortCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_DMAAbort) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f4xx_hal_i2c.o(i.I2C_DMAAbort) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.I2C_DMAError) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_i2c.o(i.I2C_DMAError) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback) for HAL_I2C_SlaveTxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback) for HAL_I2C_SlaveRxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MemRxCpltCallback) for HAL_I2C_MemRxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback) for HAL_I2C_MasterRxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_ITError) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_i2c.o(i.I2C_ITError) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_AbortCpltCallback) for HAL_I2C_AbortCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_ITError) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f4xx_hal_i2c.o(i.I2C_ITError) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback) for HAL_I2C_ListenCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_ITError) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_BTF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MemRxCpltCallback) for HAL_I2C_MemRxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_BTF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback) for HAL_I2C_MasterRxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_RXNE) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnSTOPRequestThroughIT) for I2C_WaitOnSTOPRequestThroughIT
    stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_RXNE) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MemRxCpltCallback) for HAL_I2C_MemRxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_RXNE) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback) for HAL_I2C_MasterRxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_RXNE) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterRequestRead) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_MasterRequestRead) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout) for I2C_WaitOnMasterAddressFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_MasterRequestWrite) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_MasterRequestWrite) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout) for I2C_WaitOnMasterAddressFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_BTF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MasterTxCpltCallback) for HAL_I2C_MasterTxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_BTF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MemTxCpltCallback) for HAL_I2C_MemTxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_TXE) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MasterTxCpltCallback) for HAL_I2C_MasterTxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_TXE) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MemTxCpltCallback) for HAL_I2C_MemTxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_TXE) refers to stm32f4xx_hal_i2c.o(i.I2C_MemoryTransmit_TXE_BTF) for I2C_MemoryTransmit_TXE_BTF
    stm32f4xx_hal_i2c.o(i.I2C_MemoryTransmit_TXE_BTF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MemTxCpltCallback) for HAL_I2C_MemTxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_MemoryTransmit_TXE_BTF) refers to stm32f4xx_hal_i2c.o(i.I2C_Flush_DR) for I2C_Flush_DR
    stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryRead) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryRead) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout) for I2C_WaitOnMasterAddressFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryRead) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryWrite) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryWrite) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout) for I2C_WaitOnMasterAddressFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryWrite) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_SlaveReceive_RXNE) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback) for HAL_I2C_SlaveRxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_SlaveTransmit_TXE) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback) for HAL_I2C_SlaveTxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_Slave_ADDR) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_AddrCallback) for HAL_I2C_AddrCallback
    stm32f4xx_hal_i2c.o(i.I2C_Slave_AF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback) for HAL_I2C_ListenCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_Slave_AF) refers to stm32f4xx_hal_i2c.o(i.I2C_Flush_DR) for I2C_Flush_DR
    stm32f4xx_hal_i2c.o(i.I2C_Slave_AF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback) for HAL_I2C_SlaveTxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_GetState) for HAL_DMA_GetState
    stm32f4xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f4xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32f4xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback) for HAL_I2C_SlaveRxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback) for HAL_I2C_ListenCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32f4xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout) refers to stm32f4xx_hal_i2c.o(i.I2C_IsAcknowledgeFailed) for I2C_IsAcknowledgeFailed
    stm32f4xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout) refers to stm32f4xx_hal_i2c.o(i.I2C_IsAcknowledgeFailed) for I2C_IsAcknowledgeFailed
    stm32f4xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.I2C_WaitOnSTOPRequestThroughIT) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) refers to stm32f4xx_hal_i2c.o(i.I2C_IsAcknowledgeFailed) for I2C_IsAcknowledgeFailed
    stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32f4xx.o(.constdata) for AHBPrescTable
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f4xx_hal.o(.data) for uwTickPrio
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) for HAL_RCC_GetHCLKFreq
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to system_stm32f4xx.o(.constdata) for APBPrescTable
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) for HAL_RCC_GetHCLKFreq
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to system_stm32f4xx.o(.constdata) for APBPrescTable
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) refers to lludivv7m.o(.text) for __aeabi_uldivmod
    stm32f4xx_hal_rcc.o(i.HAL_RCC_MCOConfig) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    stm32f4xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_CSSCallback) for HAL_RCC_CSSCallback
    stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_DisablePLLI2S) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnablePLLI2S) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit) refers to stm32f4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit) refers to stm32f4xx_hal.o(.data) for uwTickPrio
    stm32f4xx_hal_flash.o(i.FLASH_SetErrorCode) refers to stm32f4xx_hal_flash.o(.bss) for pFlash
    stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f4xx_hal_flash.o(i.FLASH_SetErrorCode) for FLASH_SetErrorCode
    stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f4xx_hal_flash.o(.bss) for pFlash
    stm32f4xx_hal_flash.o(i.HAL_FLASH_GetError) refers to stm32f4xx_hal_flash.o(.bss) for pFlash
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(i.FLASH_SetErrorCode) for FLASH_SetErrorCode
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(i.HAL_FLASH_OperationErrorCallback) for HAL_FLASH_OperationErrorCallback
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(i.HAL_FLASH_EndOfOperationCallback) for HAL_FLASH_EndOfOperationCallback
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_Erase_Sector) for FLASH_Erase_Sector
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_FlushCaches) for FLASH_FlushCaches
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(.bss) for pFlash
    stm32f4xx_hal_flash.o(i.HAL_FLASH_OB_Launch) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_Byte) for FLASH_Program_Byte
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_HalfWord) for FLASH_Program_HalfWord
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_Word) for FLASH_Program_Word
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_DoubleWord) for FLASH_Program_DoubleWord
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(.bss) for pFlash
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_Byte) for FLASH_Program_Byte
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_HalfWord) for FLASH_Program_HalfWord
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_Word) for FLASH_Program_Word
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_DoubleWord) for FLASH_Program_DoubleWord
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(.bss) for pFlash
    stm32f4xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(i.FLASH_OB_RDP_LevelConfig) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(i.FLASH_OB_UserConfig) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_MassErase) for FLASH_MassErase
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_Erase_Sector) for FLASH_Erase_Sector
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_FlushCaches) for FLASH_FlushCaches
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash.o(.bss) for pFlash
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_MassErase) for FLASH_MassErase
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_Erase_Sector) for FLASH_Erase_Sector
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f4xx_hal_flash.o(.bss) for pFlash
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_OB_GetWRP) for FLASH_OB_GetWRP
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_OB_GetRDP) for FLASH_OB_GetRDP
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_OB_GetUser) for FLASH_OB_GetUser
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_OB_GetBOR) for FLASH_OB_GetBOR
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP) for FLASH_OB_EnableWRP
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP) for FLASH_OB_DisableWRP
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_OB_RDP_LevelConfig) for FLASH_OB_RDP_LevelConfig
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_OB_UserConfig) for FLASH_OB_UserConfig
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_OB_BOR_LevelConfig) for FLASH_OB_BOR_LevelConfig
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash.o(.bss) for pFlash
    stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback) for HAL_GPIO_EXTI_Callback
    stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart) refers to stm32f4xx_hal_dma_ex.o(i.DMA_MultiBufferSetConfig) for DMA_MultiBufferSetConfig
    stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart_IT) refers to stm32f4xx_hal_dma_ex.o(i.DMA_MultiBufferSetConfig) for DMA_MultiBufferSetConfig
    stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift) refers to stm32f4xx_hal_dma.o(.constdata) for flagBitshiftOffset
    stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(i.HAL_DMA_DeInit) refers to stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift) for DMA_CalcBaseAndBitshift
    stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32f4xx_hal_dma.o(i.DMA_CheckFifoParam) for DMA_CheckFifoParam
    stm32f4xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift) for DMA_CalcBaseAndBitshift
    stm32f4xx_hal_dma.o(i.HAL_DMA_PollForTransfer) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(i.HAL_DMA_PollForTransfer) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_dma.o(i.HAL_DMA_Start) refers to stm32f4xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) refers to stm32f4xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32f4xx_hal_pwr.o(i.HAL_PWR_PVD_IRQHandler) refers to stm32f4xx_hal_pwr.o(i.HAL_PWR_PVDCallback) for HAL_PWR_PVDCallback
    stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_ControlVoltageScaling) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableBkUpReg) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableBkUpReg) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetPriorityGrouping) refers to stm32f4xx_hal_cortex.o(i.__NVIC_GetPriorityGrouping) for __NVIC_GetPriorityGrouping
    stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) refers to stm32f4xx_hal_cortex.o(i.__NVIC_GetPriorityGrouping) for __NVIC_GetPriorityGrouping
    stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) refers to stm32f4xx_hal_cortex.o(i.__NVIC_SetPriority) for __NVIC_SetPriority
    stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Config) refers to stm32f4xx_hal_cortex.o(i.__NVIC_SetPriority) for __NVIC_SetPriority
    stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler) refers to stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Callback) for HAL_SYSTICK_Callback
    stm32f4xx_hal.o(i.HAL_DeInit) refers to stm32f4xx_hal.o(i.HAL_MspDeInit) for HAL_MspDeInit
    stm32f4xx_hal.o(i.HAL_Delay) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal.o(i.HAL_Delay) refers to stm32f4xx_hal.o(.data) for uwTickFreq
    stm32f4xx_hal.o(i.HAL_GetTick) refers to stm32f4xx_hal.o(.data) for uwTick
    stm32f4xx_hal.o(i.HAL_GetTickFreq) refers to stm32f4xx_hal.o(.data) for uwTickFreq
    stm32f4xx_hal.o(i.HAL_GetTickPrio) refers to stm32f4xx_hal.o(.data) for uwTickPrio
    stm32f4xx_hal.o(i.HAL_IncTick) refers to stm32f4xx_hal.o(.data) for uwTick
    stm32f4xx_hal.o(i.HAL_Init) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping) for HAL_NVIC_SetPriorityGrouping
    stm32f4xx_hal.o(i.HAL_Init) refers to stm32f4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal.o(i.HAL_Init) refers to stm32f4xx_hal_msp.o(i.HAL_MspInit) for HAL_MspInit
    stm32f4xx_hal.o(i.HAL_InitTick) refers to stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Config) for HAL_SYSTICK_Config
    stm32f4xx_hal.o(i.HAL_InitTick) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    stm32f4xx_hal.o(i.HAL_InitTick) refers to stm32f4xx_hal.o(.data) for uwTickFreq
    stm32f4xx_hal.o(i.HAL_InitTick) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal.o(i.HAL_SetTickFreq) refers to stm32f4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal.o(i.HAL_SetTickFreq) refers to stm32f4xx_hal.o(.data) for uwTickFreq
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_DeInit) refers to tim.o(i.HAL_TIM_Base_MspDeInit) for HAL_TIM_Base_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init) refers to tim.o(i.HAL_TIM_Base_MspInit) for HAL_TIM_Base_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_ConfigInputStage) for TIM_TI1_ConfigInputStage
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f4xx_hal_tim.o(i.TIM_ITRx_SetConfig) for TIM_ITRx_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f4xx_hal_tim.o(i.TIM_TI2_ConfigInputStage) for TIM_TI2_ConfigInputStage
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigOCrefClear) refers to stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMATriggerCplt) for TIM_DMATriggerCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMATriggerHalfCplt) for TIM_DMATriggerHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMATriggerCplt) for TIM_DMATriggerCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMATriggerHalfCplt) for TIM_DMATriggerHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStart) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) for HAL_TIM_DMABurst_MultiReadStart
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStop) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStart) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) for HAL_TIM_DMABurst_MultiWriteStart
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStop) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_DeInit) refers to tim.o(i.HAL_TIM_Encoder_MspDeInit) for HAL_TIM_Encoder_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Init) refers to tim.o(i.HAL_TIM_Encoder_MspInit) for HAL_TIM_Encoder_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_TI2_SetConfig) for TIM_TI2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_TI3_SetConfig) for TIM_TI3_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_TI4_SetConfig) for TIM_TI4_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_DeInit) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_MspDeInit) for HAL_TIM_IC_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_MspInit) for HAL_TIM_IC_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback) for HAL_TIM_IC_CaptureCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OC_DelayElapsedCallback) for HAL_TIM_OC_DelayElapsedCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to my_timer.o(i.HAL_TIM_PeriodElapsedCallback) for HAL_TIM_PeriodElapsedCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_BreakCallback) for HAL_TIMEx_BreakCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerCallback) for HAL_TIM_TriggerCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback) for HAL_TIMEx_CommutCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC3_SetConfig) for TIM_OC3_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC4_SetConfig) for TIM_OC4_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_DeInit) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OC_MspDeInit) for HAL_TIM_OC_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OC_MspInit) for HAL_TIM_OC_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_TI2_SetConfig) for TIM_TI2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_DeInit) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspDeInit) for HAL_TIM_OnePulse_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspInit) for HAL_TIM_OnePulse_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC3_SetConfig) for TIM_OC3_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC4_SetConfig) for TIM_OC4_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_DeInit) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_MspDeInit) for HAL_TIM_PWM_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_MspInit) for HAL_TIM_PWM_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro) refers to stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) for TIM_SlaveTimer_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro_IT) refers to stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) for TIM_SlaveTimer_SetConfig
    stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback) for HAL_TIM_IC_CaptureCallback
    stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureHalfCpltCallback) for HAL_TIM_IC_CaptureHalfCpltCallback
    stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedHalfCpltCallback) for HAL_TIM_PWM_PulseFinishedHalfCpltCallback
    stm32f4xx_hal_tim.o(i.TIM_DMAError) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_ErrorCallback) for HAL_TIM_ErrorCallback
    stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) refers to my_timer.o(i.HAL_TIM_PeriodElapsedCallback) for HAL_TIM_PeriodElapsedCallback
    stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PeriodElapsedHalfCpltCallback) for HAL_TIM_PeriodElapsedHalfCpltCallback
    stm32f4xx_hal_tim.o(i.TIM_DMATriggerCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerCallback) for HAL_TIM_TriggerCallback
    stm32f4xx_hal_tim.o(i.TIM_DMATriggerHalfCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerHalfCpltCallback) for HAL_TIM_TriggerHalfCpltCallback
    stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_ConfigInputStage) for TIM_TI1_ConfigInputStage
    stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32f4xx_hal_tim.o(i.TIM_TI2_ConfigInputStage) for TIM_TI2_ConfigInputStage
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_DeInit) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspDeInit) for HAL_TIMEx_HallSensor_MspDeInit
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspInit) for HAL_TIMEx_HallSensor_MspInit
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt) for TIM_DMADelayPulseNCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN) for TIM_DMAErrorCCxN
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt) for TIM_DMADelayPulseNCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN) for TIM_DMAErrorCCxN
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback) for HAL_TIMEx_CommutCallback
    stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutHalfCpltCallback) for HAL_TIMEx_CommutHalfCpltCallback
    stm32f4xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f4xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_ErrorCallback) for HAL_TIM_ErrorCallback
    stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to stm32f4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(i.HAL_LIN_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(i.HAL_LIN_Init) refers to stm32f4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to stm32f4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA) refers to stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) for UART_Start_Receive_DMA
    stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_IT) refers to stm32f4xx_hal_uart.o(i.UART_Start_Receive_IT) for UART_Start_Receive_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f4xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback) for UART_DMARxOnlyAbortCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f4xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback) for UART_DMATxOnlyAbortCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f4xx_hal_uart.o(i.UART_DMATxAbortCallback) for UART_DMATxAbortCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f4xx_hal_uart.o(i.UART_DMARxAbortCallback) for UART_DMARxAbortCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f4xx_hal_uart.o(i.UART_EndTxTransfer) for UART_EndTxTransfer
    stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f4xx_hal_uart.o(i.HAL_UART_DeInit) refers to usart.o(i.HAL_UART_MspDeInit) for HAL_UART_MspDeInit
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.UART_Receive_IT) for UART_Receive_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to usart_app.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.UART_Transmit_IT) for UART_Transmit_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.UART_EndTransmit_IT) for UART_EndTransmit_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError) for UART_DMAAbortOnError
    stm32f4xx_hal_uart.o(i.HAL_UART_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(i.HAL_UART_Init) refers to stm32f4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive) refers to stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive_DMA) refers to stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) for UART_Start_Receive_DMA
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive_IT) refers to stm32f4xx_hal_uart.o(i.UART_Start_Receive_IT) for UART_Start_Receive_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) refers to stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMATransmitCplt) for UART_DMATransmitCplt
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMATxHalfCplt) for UART_DMATxHalfCplt
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMAError) for UART_DMAError
    stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError) refers to stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f4xx_hal_uart.o(i.UART_DMAError) refers to stm32f4xx_hal_uart.o(i.UART_EndTxTransfer) for UART_EndTxTransfer
    stm32f4xx_hal_uart.o(i.UART_DMAError) refers to stm32f4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f4xx_hal_uart.o(i.UART_DMAError) refers to stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt) refers to usart_app.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMARxAbortCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt) refers to usart_app.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback) for HAL_UART_RxHalfCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMATransmitCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMATxAbortCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMATxHalfCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UART_TxHalfCpltCallback) for HAL_UART_TxHalfCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32f4xx_hal_uart.o(i.UART_EndTransmit_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32f4xx_hal_uart.o(i.UART_Receive_IT) refers to usart_app.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(i.UART_Receive_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32f4xx_hal_uart.o(i.UART_SetConfig) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) for HAL_RCC_GetPCLK2Freq
    stm32f4xx_hal_uart.o(i.UART_SetConfig) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32f4xx_hal_uart.o(i.UART_SetConfig) refers to lludivv7m.o(.text) for __aeabi_uldivmod
    stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt) for UART_DMAReceiveCplt
    stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt) for UART_DMARxHalfCplt
    stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMAError) for UART_DMAError
    stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) refers to stm32f4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    system_stm32f4xx.o(i.SystemCoreClockUpdate) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    system_stm32f4xx.o(i.SystemCoreClockUpdate) refers to system_stm32f4xx.o(.constdata) for AHBPrescTable
    ringbuffer.o(i.rt_ringbuffer_data_len) refers to ringbuffer.o(i.rt_ringbuffer_status) for rt_ringbuffer_status
    ringbuffer.o(i.rt_ringbuffer_get) refers to assert.o(.text) for __aeabi_assert
    ringbuffer.o(i.rt_ringbuffer_get) refers to ringbuffer.o(i.rt_ringbuffer_data_len) for rt_ringbuffer_data_len
    ringbuffer.o(i.rt_ringbuffer_get) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    ringbuffer.o(i.rt_ringbuffer_getchar) refers to assert.o(.text) for __aeabi_assert
    ringbuffer.o(i.rt_ringbuffer_getchar) refers to ringbuffer.o(i.rt_ringbuffer_data_len) for rt_ringbuffer_data_len
    ringbuffer.o(i.rt_ringbuffer_init) refers to assert.o(.text) for __aeabi_assert
    ringbuffer.o(i.rt_ringbuffer_peek) refers to assert.o(.text) for __aeabi_assert
    ringbuffer.o(i.rt_ringbuffer_peek) refers to ringbuffer.o(i.rt_ringbuffer_data_len) for rt_ringbuffer_data_len
    ringbuffer.o(i.rt_ringbuffer_put) refers to assert.o(.text) for __aeabi_assert
    ringbuffer.o(i.rt_ringbuffer_put) refers to ringbuffer.o(i.rt_ringbuffer_data_len) for rt_ringbuffer_data_len
    ringbuffer.o(i.rt_ringbuffer_put) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    ringbuffer.o(i.rt_ringbuffer_put_force) refers to assert.o(.text) for __aeabi_assert
    ringbuffer.o(i.rt_ringbuffer_put_force) refers to ringbuffer.o(i.rt_ringbuffer_data_len) for rt_ringbuffer_data_len
    ringbuffer.o(i.rt_ringbuffer_put_force) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    ringbuffer.o(i.rt_ringbuffer_putchar) refers to assert.o(.text) for __aeabi_assert
    ringbuffer.o(i.rt_ringbuffer_putchar) refers to ringbuffer.o(i.rt_ringbuffer_data_len) for rt_ringbuffer_data_len
    ringbuffer.o(i.rt_ringbuffer_putchar_force) refers to assert.o(.text) for __aeabi_assert
    ringbuffer.o(i.rt_ringbuffer_putchar_force) refers to ringbuffer.o(i.rt_ringbuffer_status) for rt_ringbuffer_status
    ringbuffer.o(i.rt_ringbuffer_reset) refers to assert.o(.text) for __aeabi_assert
    software_iic.o(i.Delay_us) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    software_iic.o(i.IIC_Anolog_Normalize) refers to software_iic.o(i.IIC_WriteBytes) for IIC_WriteBytes
    software_iic.o(i.IIC_Delay) refers to software_iic.o(i.Delay_us) for Delay_us
    software_iic.o(i.IIC_Get_Anolog) refers to software_iic.o(i.IIC_ReadBytes) for IIC_ReadBytes
    software_iic.o(i.IIC_Get_Digtal) refers to software_iic.o(i.IIC_ReadBytes) for IIC_ReadBytes
    software_iic.o(i.IIC_Get_Offset) refers to software_iic.o(i.IIC_ReadBytes) for IIC_ReadBytes
    software_iic.o(i.IIC_Get_Single_Anolog) refers to software_iic.o(i.IIC_ReadBytes) for IIC_ReadBytes
    software_iic.o(i.IIC_ReadByte) refers to software_iic.o(i.IIC_Start) for IIC_Start
    software_iic.o(i.IIC_ReadByte) refers to software_iic.o(i.IIC_SendByte) for IIC_SendByte
    software_iic.o(i.IIC_ReadByte) refers to software_iic.o(i.IIC_RecvByte) for IIC_RecvByte
    software_iic.o(i.IIC_ReadByte) refers to software_iic.o(i.IIC_SendNAck) for IIC_SendNAck
    software_iic.o(i.IIC_ReadByte) refers to software_iic.o(i.IIC_Stop) for IIC_Stop
    software_iic.o(i.IIC_ReadBytes) refers to software_iic.o(i.IIC_Start) for IIC_Start
    software_iic.o(i.IIC_ReadBytes) refers to software_iic.o(i.IIC_SendByte) for IIC_SendByte
    software_iic.o(i.IIC_ReadBytes) refers to software_iic.o(i.IIC_Stop) for IIC_Stop
    software_iic.o(i.IIC_ReadBytes) refers to software_iic.o(i.IIC_RecvByte) for IIC_RecvByte
    software_iic.o(i.IIC_ReadBytes) refers to software_iic.o(i.IIC_SendNAck) for IIC_SendNAck
    software_iic.o(i.IIC_ReadBytes) refers to software_iic.o(i.IIC_SendAck) for IIC_SendAck
    software_iic.o(i.IIC_RecvByte) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    software_iic.o(i.IIC_RecvByte) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    software_iic.o(i.IIC_RecvByte) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    software_iic.o(i.IIC_RecvByte) refers to software_iic.o(i.IIC_Delay) for IIC_Delay
    software_iic.o(i.IIC_RecvByte) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_ReadPin) for HAL_GPIO_ReadPin
    software_iic.o(i.IIC_SendAck) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    software_iic.o(i.IIC_SendAck) refers to software_iic.o(i.IIC_Delay) for IIC_Delay
    software_iic.o(i.IIC_SendByte) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    software_iic.o(i.IIC_SendByte) refers to software_iic.o(i.IIC_Delay) for IIC_Delay
    software_iic.o(i.IIC_SendByte) refers to software_iic.o(i.IIC_WaitAck) for IIC_WaitAck
    software_iic.o(i.IIC_SendNAck) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    software_iic.o(i.IIC_SendNAck) refers to software_iic.o(i.IIC_Delay) for IIC_Delay
    software_iic.o(i.IIC_Start) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    software_iic.o(i.IIC_Start) refers to software_iic.o(i.IIC_Delay) for IIC_Delay
    software_iic.o(i.IIC_Stop) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    software_iic.o(i.IIC_Stop) refers to software_iic.o(i.IIC_Delay) for IIC_Delay
    software_iic.o(i.IIC_WaitAck) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    software_iic.o(i.IIC_WaitAck) refers to software_iic.o(i.IIC_Delay) for IIC_Delay
    software_iic.o(i.IIC_WaitAck) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_ReadPin) for HAL_GPIO_ReadPin
    software_iic.o(i.IIC_WriteByte) refers to software_iic.o(i.IIC_Start) for IIC_Start
    software_iic.o(i.IIC_WriteByte) refers to software_iic.o(i.IIC_SendByte) for IIC_SendByte
    software_iic.o(i.IIC_WriteByte) refers to software_iic.o(i.IIC_Stop) for IIC_Stop
    software_iic.o(i.IIC_WriteBytes) refers to software_iic.o(i.IIC_Start) for IIC_Start
    software_iic.o(i.IIC_WriteBytes) refers to software_iic.o(i.IIC_SendByte) for IIC_SendByte
    software_iic.o(i.IIC_WriteBytes) refers to software_iic.o(i.IIC_Stop) for IIC_Stop
    software_iic.o(i.Ping) refers to software_iic.o(i.IIC_ReadBytes) for IIC_ReadBytes
    pid.o(i.pid_calculate_incremental) refers to pid.o(i.pid_formula_incremental) for pid_formula_incremental
    pid.o(i.pid_calculate_incremental) refers to pid.o(i.pid_out_limit) for pid_out_limit
    pid.o(i.pid_calculate_positional) refers to pid.o(i.pid_formula_positional) for pid_formula_positional
    pid.o(i.pid_calculate_positional) refers to pid.o(i.pid_out_limit) for pid_out_limit
    motor_driver.o(i.DRV8871_Control) refers to motor_driver.o(i.Speed1000_To_PWM) for Speed1000_To_PWM
    motor_driver.o(i.DRV8871_Control) refers to motor_driver.o(i.Set_Pin_Mode) for Set_Pin_Mode
    motor_driver.o(i.Float_To_Speed1000) refers to roundf.o(i.__hardfp_roundf) for __hardfp_roundf
    motor_driver.o(i.Motor_Create) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start) for HAL_TIM_PWM_Start
    motor_driver.o(i.Motor_Create) refers to motor_driver.o(i.DRV8871_Control) for DRV8871_Control
    motor_driver.o(i.Motor_Enable) refers to motor_driver.o(i.Motor_ValidateParams) for Motor_ValidateParams
    motor_driver.o(i.Motor_Enable) refers to motor_driver.o(i.DRV8871_Control) for DRV8871_Control
    motor_driver.o(i.Motor_GetState) refers to motor_driver.o(i.Motor_ValidateParams) for Motor_ValidateParams
    motor_driver.o(i.Motor_SetDecayMode) refers to motor_driver.o(i.Motor_ValidateParams) for Motor_ValidateParams
    motor_driver.o(i.Motor_SetDecayMode) refers to motor_driver.o(i.Motor_SetSpeed) for Motor_SetSpeed
    motor_driver.o(i.Motor_SetSpeed) refers to motor_driver.o(i.Motor_ValidateParams) for Motor_ValidateParams
    motor_driver.o(i.Motor_SetSpeed) refers to motor_driver.o(i.Motor_ValidateFloatSpeed) for Motor_ValidateFloatSpeed
    motor_driver.o(i.Motor_SetSpeed) refers to motor_driver.o(i.Float_To_Speed1000) for Float_To_Speed1000
    motor_driver.o(i.Motor_SetSpeed) refers to motor_driver.o(i.DRV8871_Control) for DRV8871_Control
    motor_driver.o(i.Motor_Stop) refers to motor_driver.o(i.Motor_ValidateParams) for Motor_ValidateParams
    motor_driver.o(i.Motor_Stop) refers to motor_driver.o(i.DRV8871_Control) for DRV8871_Control
    motor_driver.o(i.Set_Pin_Mode) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    motor_driver.o(i.Set_Pin_Mode) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    motor_driver.o(i.Set_Pin_Mode) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    oled.o(i.OLED_Allfill) refers to oled.o(i.OLED_Write_cmd) for OLED_Write_cmd
    oled.o(i.OLED_Allfill) refers to oled.o(i.OLED_Write_data) for OLED_Write_data
    oled.o(i.OLED_Clear) refers to oled.o(i.OLED_Write_cmd) for OLED_Write_cmd
    oled.o(i.OLED_Clear) refers to oled.o(i.OLED_Write_data) for OLED_Write_data
    oled.o(i.OLED_Display_Off) refers to oled.o(i.OLED_Write_cmd) for OLED_Write_cmd
    oled.o(i.OLED_Display_On) refers to oled.o(i.OLED_Write_cmd) for OLED_Write_cmd
    oled.o(i.OLED_Init) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_Write_cmd) for OLED_Write_cmd
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_Clear) for OLED_Clear
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_Set_Position) for OLED_Set_Position
    oled.o(i.OLED_Init) refers to oled.o(.data) for initcmd1
    oled.o(i.OLED_Set_Position) refers to oled.o(i.OLED_Write_cmd) for OLED_Write_cmd
    oled.o(i.OLED_ShowChar) refers to oled.o(i.OLED_Set_Position) for OLED_Set_Position
    oled.o(i.OLED_ShowChar) refers to oled.o(i.OLED_Write_data) for OLED_Write_data
    oled.o(i.OLED_ShowChar) refers to oled.o(.constdata) for F8X16
    oled.o(i.OLED_ShowFloat) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowHanzi) refers to oled.o(i.OLED_Set_Position) for OLED_Set_Position
    oled.o(i.OLED_ShowHanzi) refers to oled.o(i.OLED_Write_data) for OLED_Write_data
    oled.o(i.OLED_ShowHanzi) refers to oled.o(.constdata) for Hzk
    oled.o(i.OLED_ShowHzbig) refers to oled.o(i.OLED_Set_Position) for OLED_Set_Position
    oled.o(i.OLED_ShowHzbig) refers to oled.o(i.OLED_Write_data) for OLED_Write_data
    oled.o(i.OLED_ShowHzbig) refers to oled.o(.constdata) for Hzb
    oled.o(i.OLED_ShowNum) refers to oled.o(i.OLED_Pow) for OLED_Pow
    oled.o(i.OLED_ShowNum) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowPic) refers to oled.o(i.OLED_Set_Position) for OLED_Set_Position
    oled.o(i.OLED_ShowPic) refers to oled.o(i.OLED_Write_data) for OLED_Write_data
    oled.o(i.OLED_ShowStr) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_Write_cmd) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) for HAL_I2C_Mem_Write
    oled.o(i.OLED_Write_cmd) refers to i2c.o(.bss) for hi2c2
    oled.o(i.OLED_Write_data) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) for HAL_I2C_Mem_Write
    oled.o(i.OLED_Write_data) refers to i2c.o(.bss) for hi2c2
    encoder_driver.o(i.Encoder_Driver_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start) for HAL_TIM_Encoder_Start
    encoder_driver.o(i.Encoder_Driver_Update) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    encoder_driver.o(i.Encoder_Driver_Update) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    encoder_driver.o(i.Encoder_Driver_Update) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    mpu6050_hal.o(i.MPU6050_CalculateEuler) refers to atan2f.o(i.__hardfp_atan2f) for __hardfp_atan2f
    mpu6050_hal.o(i.MPU6050_CalculateEuler) refers to sqrtf.o(i.__hardfp_sqrtf) for __hardfp_sqrtf
    mpu6050_hal.o(i.MPU6050_CalculateEuler) refers to mpu6050_hal.o(.data) for first_run
    mpu6050_hal.o(i.MPU6050_CalibrateGyro) refers to mpu6050_hal.o(i.MPU6050_ReadRawData) for MPU6050_ReadRawData
    mpu6050_hal.o(i.MPU6050_CalibrateGyro) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    mpu6050_hal.o(i.MPU6050_CalibrateGyro) refers to mpu6050_hal.o(.data) for gyro_scale
    mpu6050_hal.o(i.MPU6050_Config) refers to mpu6050_hal.o(i.MPU6050_WriteReg) for MPU6050_WriteReg
    mpu6050_hal.o(i.MPU6050_Config) refers to mpu6050_hal.o(i.MPU6050_UpdateScales) for MPU6050_UpdateScales
    mpu6050_hal.o(i.MPU6050_Config) refers to mpu6050_hal.o(.data) for mpu6050_config
    mpu6050_hal.o(i.MPU6050_GetTemperature) refers to mpu6050_hal.o(i.MPU6050_ReadRawData) for MPU6050_ReadRawData
    mpu6050_hal.o(i.MPU6050_Init) refers to mpu6050_hal.o(i.MPU6050_TestConnection) for MPU6050_TestConnection
    mpu6050_hal.o(i.MPU6050_Init) refers to mpu6050_hal.o(i.MPU6050_Reset) for MPU6050_Reset
    mpu6050_hal.o(i.MPU6050_Init) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    mpu6050_hal.o(i.MPU6050_Init) refers to mpu6050_hal.o(i.MPU6050_WriteReg) for MPU6050_WriteReg
    mpu6050_hal.o(i.MPU6050_Init) refers to mpu6050_hal.o(i.MPU6050_Config) for MPU6050_Config
    mpu6050_hal.o(i.MPU6050_Init) refers to mpu6050_hal.o(i.MPU6050_CalibrateGyro) for MPU6050_CalibrateGyro
    mpu6050_hal.o(i.MPU6050_Init) refers to mpu6050_hal.o(.data) for mpu6050_i2c
    mpu6050_hal.o(i.MPU6050_ReadData) refers to mpu6050_hal.o(i.MPU6050_ReadRawData) for MPU6050_ReadRawData
    mpu6050_hal.o(i.MPU6050_ReadData) refers to mpu6050_hal.o(.data) for accel_scale
    mpu6050_hal.o(i.MPU6050_ReadRawData) refers to mpu6050_hal.o(i.MPU6050_ReadRegs) for MPU6050_ReadRegs
    mpu6050_hal.o(i.MPU6050_ReadReg) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read) for HAL_I2C_Mem_Read
    mpu6050_hal.o(i.MPU6050_ReadReg) refers to mpu6050_hal.o(.data) for mpu6050_addr
    mpu6050_hal.o(i.MPU6050_ReadRegs) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read) for HAL_I2C_Mem_Read
    mpu6050_hal.o(i.MPU6050_ReadRegs) refers to mpu6050_hal.o(.data) for mpu6050_addr
    mpu6050_hal.o(i.MPU6050_Reset) refers to mpu6050_hal.o(i.MPU6050_WriteReg) for MPU6050_WriteReg
    mpu6050_hal.o(i.MPU6050_TestConnection) refers to mpu6050_hal.o(i.MPU6050_ReadReg) for MPU6050_ReadReg
    mpu6050_hal.o(i.MPU6050_UpdateScales) refers to mpu6050_hal.o(.data) for mpu6050_config
    mpu6050_hal.o(i.MPU6050_WriteReg) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) for HAL_I2C_Mem_Write
    mpu6050_hal.o(i.MPU6050_WriteReg) refers to mpu6050_hal.o(.data) for mpu6050_addr
    led_driver.o(i.led_disp) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    led_driver.o(i.led_disp) refers to led_driver.o(.data) for temp_old
    key_driver.o(i.key_read) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_ReadPin) for HAL_GPIO_ReadPin
    usart_app.o(i.HAL_UARTEx_RxEventCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop) for HAL_UART_DMAStop
    usart_app.o(i.HAL_UARTEx_RxEventCallback) refers to ringbuffer.o(i.rt_ringbuffer_put) for rt_ringbuffer_put
    usart_app.o(i.HAL_UARTEx_RxEventCallback) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    usart_app.o(i.HAL_UARTEx_RxEventCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA) for HAL_UARTEx_ReceiveToIdle_DMA
    usart_app.o(i.HAL_UARTEx_RxEventCallback) refers to usart_app.o(.bss) for uart_rx_dma_buffer
    usart_app.o(i.HAL_UARTEx_RxEventCallback) refers to usart.o(.bss) for huart1
    usart_app.o(i.my_printf) refers to vsnprintf.o(.text) for vsnprintf
    usart_app.o(i.my_printf) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    usart_app.o(i.uart_init) refers to ringbuffer.o(i.rt_ringbuffer_init) for rt_ringbuffer_init
    usart_app.o(i.uart_init) refers to stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA) for HAL_UARTEx_ReceiveToIdle_DMA
    usart_app.o(i.uart_init) refers to usart_app.o(.bss) for ringbuffer_pool
    usart_app.o(i.uart_init) refers to usart.o(.bss) for huart1
    usart_app.o(i.uart_task) refers to _scanf_int.o(.text) for _scanf_int
    usart_app.o(i.uart_task) refers to ringbuffer.o(i.rt_ringbuffer_data_len) for rt_ringbuffer_data_len
    usart_app.o(i.uart_task) refers to ringbuffer.o(i.rt_ringbuffer_get) for rt_ringbuffer_get
    usart_app.o(i.uart_task) refers to __0sscanf.o(.text) for __0sscanf
    usart_app.o(i.uart_task) refers to pid.o(i.pid_set_target) for pid_set_target
    usart_app.o(i.uart_task) refers to usart_app.o(i.my_printf) for my_printf
    usart_app.o(i.uart_task) refers to motor_app.o(i.motor_set_r) for motor_set_r
    usart_app.o(i.uart_task) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    usart_app.o(i.uart_task) refers to usart_app.o(.bss) for uart_ringbuffer
    usart_app.o(i.uart_task) refers to pid_app.o(.bss) for pid_speed_left
    usart_app.o(i.uart_task) refers to usart.o(.bss) for huart1
    led_app.o(i.led_task) refers to led_driver.o(i.led_disp) for led_disp
    led_app.o(i.led_task) refers to led_app.o(.data) for led_rgb
    key_app.o(i.key_task) refers to key_driver.o(i.key_read) for key_read
    key_app.o(i.key_task) refers to key_app.o(.data) for key_old
    key_app.o(i.key_task) refers to pid_app.o(.data) for pid_running
    key_app.o(i.key_task) refers to led_app.o(.data) for led_rgb
    key_app.o(i.key_task) refers to my_timer.o(.data) for system_mode
    oled_app.o(i.my_oled_init) refers to oled.o(i.OLED_Init) for OLED_Init
    oled_app.o(i.oled_printf) refers to vsnprintf.o(.text) for vsnprintf
    oled_app.o(i.oled_printf) refers to oled.o(i.OLED_ShowStr) for OLED_ShowStr
    oled_app.o(i.oled_task) refers to mpu6050_app.o(i.get_yaw) for get_yaw
    oled_app.o(i.oled_task) refers to oled_app.o(i.oled_printf) for oled_printf
    oled_app.o(i.oled_task) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    oled_app.o(i.oled_task) refers to my_timer.o(.data) for system_mode
    oled_app.o(i.oled_task) refers to gray_app.o(.data) for Digtal
    motor_app.o(i.Motor_Init) refers to motor_driver.o(i.Motor_Create) for Motor_Create
    motor_app.o(i.Motor_Init) refers to tim.o(.bss) for htim1
    motor_app.o(i.Motor_Init) refers to motor_app.o(.bss) for left_motor
    motor_app.o(i.motor_break) refers to motor_driver.o(i.Motor_Stop) for Motor_Stop
    motor_app.o(i.motor_break) refers to motor_app.o(.bss) for right_motor
    motor_app.o(i.motor_set_l) refers to motor_driver.o(i.Motor_SetSpeed) for Motor_SetSpeed
    motor_app.o(i.motor_set_l) refers to motor_app.o(.bss) for left_motor
    motor_app.o(i.motor_set_r) refers to motor_driver.o(i.Motor_SetSpeed) for Motor_SetSpeed
    motor_app.o(i.motor_set_r) refers to motor_app.o(.bss) for right_motor
    encoder_app.o(i.Encoder_Init) refers to encoder_driver.o(i.Encoder_Driver_Init) for Encoder_Driver_Init
    encoder_app.o(i.Encoder_Init) refers to tim.o(.bss) for htim3
    encoder_app.o(i.Encoder_Init) refers to encoder_app.o(.bss) for left_encoder
    encoder_app.o(i.Encoder_Task) refers to encoder_driver.o(i.Encoder_Driver_Update) for Encoder_Driver_Update
    encoder_app.o(i.Encoder_Task) refers to encoder_app.o(.bss) for left_encoder
    pid_app.o(i.Angle_PID_control) refers to mpu6050_app.o(i.get_yaw) for get_yaw
    pid_app.o(i.Angle_PID_control) refers to pid.o(i.pid_calculate_positional) for pid_calculate_positional
    pid_app.o(i.Angle_PID_control) refers to pid.o(i.pid_constrain) for pid_constrain
    pid_app.o(i.Angle_PID_control) refers to pid.o(i.pid_set_target) for pid_set_target
    pid_app.o(i.Angle_PID_control) refers to pid_app.o(.bss) for pid_angle
    pid_app.o(i.Angle_PID_control) refers to pid_app.o(.data) for pid_params_angle
    pid_app.o(i.Line_PID_control) refers to pid.o(i.pid_calculate_positional) for pid_calculate_positional
    pid_app.o(i.Line_PID_control) refers to pid.o(i.pid_constrain) for pid_constrain
    pid_app.o(i.Line_PID_control) refers to pid.o(i.pid_set_target) for pid_set_target
    pid_app.o(i.Line_PID_control) refers to gray_app.o(.data) for g_line_position_error
    pid_app.o(i.Line_PID_control) refers to pid_app.o(.bss) for pid_line
    pid_app.o(i.Line_PID_control) refers to pid_app.o(.data) for pid_params_line
    pid_app.o(i.PID_Init) refers to pid.o(i.pid_init) for pid_init
    pid_app.o(i.PID_Init) refers to pid.o(i.pid_set_target) for pid_set_target
    pid_app.o(i.PID_Init) refers to pid_app.o(.data) for pid_params_left
    pid_app.o(i.PID_Init) refers to pid_app.o(.bss) for pid_speed_left
    pid_app.o(i.PID_Task) refers to pid.o(i.pid_set_target) for pid_set_target
    pid_app.o(i.PID_Task) refers to pid_app.o(i.Angle_PID_control) for Angle_PID_control
    pid_app.o(i.PID_Task) refers to pid_app.o(i.Line_PID_control) for Line_PID_control
    pid_app.o(i.PID_Task) refers to pid.o(i.pid_calculate_positional) for pid_calculate_positional
    pid_app.o(i.PID_Task) refers to pid.o(i.pid_constrain) for pid_constrain
    pid_app.o(i.PID_Task) refers to motor_app.o(i.motor_set_l) for motor_set_l
    pid_app.o(i.PID_Task) refers to motor_app.o(i.motor_set_r) for motor_set_r
    pid_app.o(i.PID_Task) refers to pid_app.o(.data) for pid_running
    pid_app.o(i.PID_Task) refers to my_timer.o(.data) for system_mode
    pid_app.o(i.PID_Task) refers to pid_app.o(.bss) for pid_angle
    pid_app.o(i.PID_Task) refers to encoder_app.o(.bss) for left_encoder
    gray_app.o(i.Gray_Init) refers to software_iic.o(i.Ping) for Ping
    gray_app.o(i.Gray_Init) refers to usart_app.o(i.my_printf) for my_printf
    gray_app.o(i.Gray_Init) refers to usart.o(.bss) for huart1
    gray_app.o(i.Gray_Task) refers to software_iic.o(i.IIC_Get_Digtal) for IIC_Get_Digtal
    gray_app.o(i.Gray_Task) refers to gray_app.o(.data) for Digtal
    mpu6050_app.o(i.bno080_task) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    mpu6050_app.o(i.bno080_task) refers to mpu6050_hal.o(i.MPU6050_ReadData) for MPU6050_ReadData
    mpu6050_app.o(i.bno080_task) refers to mpu6050_hal.o(i.MPU6050_CalculateEuler) for MPU6050_CalculateEuler
    mpu6050_app.o(i.bno080_task) refers to mpu6050_app.o(.data) for last_update_time
    mpu6050_app.o(i.bno080_task) refers to mpu6050_app.o(.bss) for mpu6050_data
    mpu6050_app.o(i.calibrateGyro) refers to mpu6050_hal.o(i.MPU6050_CalibrateGyro) for MPU6050_CalibrateGyro
    mpu6050_app.o(i.convert_to_continuous_yaw) refers to mpu6050_app.o(.data) for g_is_yaw_initialized
    mpu6050_app.o(i.dataAvailable) refers to mpu6050_app.o(.data) for data_ready
    mpu6050_app.o(i.getAccelX) refers to mpu6050_app.o(.bss) for mpu6050_data
    mpu6050_app.o(i.getAccelY) refers to mpu6050_app.o(.bss) for mpu6050_data
    mpu6050_app.o(i.getAccelZ) refers to mpu6050_app.o(.bss) for mpu6050_data
    mpu6050_app.o(i.getGyroX) refers to mpu6050_app.o(.bss) for mpu6050_data
    mpu6050_app.o(i.getGyroY) refers to mpu6050_app.o(.bss) for mpu6050_data
    mpu6050_app.o(i.getGyroZ) refers to mpu6050_app.o(.bss) for mpu6050_data
    mpu6050_app.o(i.getTemperature) refers to mpu6050_app.o(.bss) for mpu6050_data
    mpu6050_app.o(i.get_pitch) refers to mpu6050_app.o(.data) for pitch
    mpu6050_app.o(i.get_roll) refers to mpu6050_app.o(.data) for roll
    mpu6050_app.o(i.get_yaw) refers to mpu6050_app.o(i.convert_to_continuous_yaw) for convert_to_continuous_yaw
    mpu6050_app.o(i.get_yaw) refers to mpu6050_app.o(.data) for yaw
    mpu6050_app.o(i.my_bno080_init) refers to usart_app.o(i.my_printf) for my_printf
    mpu6050_app.o(i.my_bno080_init) refers to mpu6050_hal.o(i.MPU6050_Init) for MPU6050_Init
    mpu6050_app.o(i.my_bno080_init) refers to mpu6050_hal.o(i.MPU6050_TestConnection) for MPU6050_TestConnection
    mpu6050_app.o(i.my_bno080_init) refers to mpu6050_hal.o(i.MPU6050_Config) for MPU6050_Config
    mpu6050_app.o(i.my_bno080_init) refers to mpu6050_hal.o(i.MPU6050_CalibrateGyro) for MPU6050_CalibrateGyro
    mpu6050_app.o(i.my_bno080_init) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    mpu6050_app.o(i.my_bno080_init) refers to usart.o(.bss) for huart1
    mpu6050_app.o(i.my_bno080_init) refers to i2c.o(.bss) for hi2c1
    mpu6050_app.o(i.my_bno080_init) refers to mpu6050_app.o(.constdata) for .constdata
    mpu6050_app.o(i.softReset) refers to mpu6050_hal.o(i.MPU6050_Reset) for MPU6050_Reset
    my_scheduler.o(i.all_task_init) refers to oled_app.o(i.my_oled_init) for my_oled_init
    my_scheduler.o(i.all_task_init) refers to usart_app.o(i.uart_init) for uart_init
    my_scheduler.o(i.all_task_init) refers to mpu6050_app.o(i.my_bno080_init) for my_bno080_init
    my_scheduler.o(i.all_task_init) refers to led_app.o(i.led_init) for led_init
    my_scheduler.o(i.all_task_init) refers to pid_app.o(i.PID_Init) for PID_Init
    my_scheduler.o(i.all_task_init) refers to encoder_app.o(i.Encoder_Init) for Encoder_Init
    my_scheduler.o(i.all_task_init) refers to motor_app.o(i.Motor_Init) for Motor_Init
    my_scheduler.o(i.all_task_init) refers to gray_app.o(i.Gray_Init) for Gray_Init
    my_scheduler.o(i.all_task_init) refers to my_timer.o(i.timer_init) for timer_init
    my_scheduler.o(i.all_task_init) refers to my_scheduler.o(.data) for task_num
    my_scheduler.o(i.all_task_run) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    my_scheduler.o(i.all_task_run) refers to my_scheduler.o(.data) for all_task
    my_scheduler.o(.data) refers to led_app.o(i.led_task) for led_task
    my_scheduler.o(.data) refers to mpu6050_app.o(i.bno080_task) for bno080_task
    my_scheduler.o(.data) refers to usart_app.o(i.uart_task) for uart_task
    my_scheduler.o(.data) refers to key_app.o(i.key_task) for key_task
    my_scheduler.o(.data) refers to oled_app.o(i.oled_task) for oled_task
    my_timer.o(i.Car_State_Update) refers to pid.o(i.pid_set_target) for pid_set_target
    my_timer.o(i.Car_State_Update) refers to pid.o(i.pid_reset) for pid_reset
    my_timer.o(i.Car_State_Update) refers to led_app.o(.data) for led_rgb
    my_timer.o(i.Car_State_Update) refers to my_timer.o(.data) for distance
    my_timer.o(i.Car_State_Update) refers to pid_app.o(.data) for stop_flat
    my_timer.o(i.Car_State_Update) refers to pid_app.o(.bss) for pid_angle
    my_timer.o(i.HAL_TIM_PeriodElapsedCallback) refers to encoder_app.o(i.Encoder_Task) for Encoder_Task
    my_timer.o(i.HAL_TIM_PeriodElapsedCallback) refers to gray_app.o(i.Gray_Task) for Gray_Task
    my_timer.o(i.HAL_TIM_PeriodElapsedCallback) refers to mpu6050_app.o(i.bno080_task) for bno080_task
    my_timer.o(i.HAL_TIM_PeriodElapsedCallback) refers to pid_app.o(i.PID_Task) for PID_Task
    my_timer.o(i.HAL_TIM_PeriodElapsedCallback) refers to my_timer.o(i.Car_State_Update) for Car_State_Update
    my_timer.o(i.HAL_TIM_PeriodElapsedCallback) refers to tim.o(.bss) for htim2
    my_timer.o(i.HAL_TIM_PeriodElapsedCallback) refers to my_timer.o(.data) for measure_timer5ms
    my_timer.o(i.HAL_TIM_PeriodElapsedCallback) refers to encoder_app.o(.bss) for left_encoder
    my_timer.o(i.HAL_TIM_PeriodElapsedCallback) refers to gray_app.o(.data) for Digtal
    my_timer.o(i.HAL_TIM_PeriodElapsedCallback) refers to led_app.o(.data) for led_rgb
    my_timer.o(i.timer_init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_IT) for HAL_TIM_Base_Start_IT
    my_timer.o(i.timer_init) refers to tim.o(.bss) for htim2
    vsnprintf.o(.text) refers (Special) to _printf_a.o(.ARM.Collect$$_printf_percent$$00000006) for _printf_a
    vsnprintf.o(.text) refers (Special) to _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) for _printf_c
    vsnprintf.o(.text) refers (Special) to _printf_charcount.o(.text) for _printf_charcount
    vsnprintf.o(.text) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    vsnprintf.o(.text) refers (Special) to _printf_e.o(.ARM.Collect$$_printf_percent$$00000004) for _printf_e
    vsnprintf.o(.text) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    vsnprintf.o(.text) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    vsnprintf.o(.text) refers (Special) to printf2.o(x$fpl$printf2) for _printf_fp_hex
    vsnprintf.o(.text) refers (Special) to _printf_g.o(.ARM.Collect$$_printf_percent$$00000005) for _printf_g
    vsnprintf.o(.text) refers (Special) to _printf_i.o(.ARM.Collect$$_printf_percent$$00000008) for _printf_i
    vsnprintf.o(.text) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    vsnprintf.o(.text) refers (Special) to _printf_l.o(.ARM.Collect$$_printf_percent$$00000012) for _printf_l
    vsnprintf.o(.text) refers (Special) to _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015) for _printf_lc
    vsnprintf.o(.text) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    vsnprintf.o(.text) refers (Special) to _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E) for _printf_lld
    vsnprintf.o(.text) refers (Special) to _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D) for _printf_lli
    vsnprintf.o(.text) refers (Special) to _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010) for _printf_llo
    vsnprintf.o(.text) refers (Special) to _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F) for _printf_llu
    vsnprintf.o(.text) refers (Special) to _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011) for _printf_llx
    vsnprintf.o(.text) refers (Special) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    vsnprintf.o(.text) refers (Special) to _printf_hex_int_ll_ptr.o(.text) for _printf_longlong_hex
    vsnprintf.o(.text) refers (Special) to _printf_oct_int_ll.o(.text) for _printf_longlong_oct
    vsnprintf.o(.text) refers (Special) to _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016) for _printf_ls
    vsnprintf.o(.text) refers (Special) to _printf_n.o(.ARM.Collect$$_printf_percent$$00000001) for _printf_n
    vsnprintf.o(.text) refers (Special) to _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B) for _printf_o
    vsnprintf.o(.text) refers (Special) to _printf_p.o(.ARM.Collect$$_printf_percent$$00000002) for _printf_p
    vsnprintf.o(.text) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    vsnprintf.o(.text) refers (Special) to _printf_pad.o(.text) for _printf_post_padding
    vsnprintf.o(.text) refers (Special) to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    vsnprintf.o(.text) refers (Special) to _printf_str.o(.text) for _printf_str
    vsnprintf.o(.text) refers (Special) to _printf_truncate.o(.text) for _printf_truncate_signed
    vsnprintf.o(.text) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    vsnprintf.o(.text) refers (Special) to _printf_wctomb.o(.text) for _printf_wctomb
    vsnprintf.o(.text) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    vsnprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    vsnprintf.o(.text) refers to _sputc.o(.text) for _sputc
    vsnprintf.o(.text) refers to _snputc.o(.text) for _snputc
    __0sscanf.o(.text) refers to scanf_char.o(.text) for __vfscanf_char
    __0sscanf.o(.text) refers to _sgetc.o(.text) for _sgetc
    _scanf_int.o(.text) refers to _chval.o(.text) for _chval
    assert.o(.text) refers to assert_puts.o(.text) for __assert_puts
    assert.o(.text) refers to abort.o(.text) for abort
    rt_memcpy_v6.o(.text) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    __main.o(!!!main) refers to __rtentry.o(.ARM.Collect$$rtentry$$00000000) for __rt_entry
    d2f.o(x$fpl$d2f) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    d2f.o(x$fpl$d2f) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    d2f.o(x$fpl$d2f) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    ddiv.o(x$fpl$drdiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(x$fpl$drdiv) refers to ddiv.o(x$fpl$ddiv) for ddiv_entry
    ddiv.o(x$fpl$ddiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(x$fpl$ddiv) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    ddiv.o(x$fpl$ddiv) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    f2d.o(x$fpl$f2d) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    f2d.o(x$fpl$f2d) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    f2d.o(x$fpl$f2d) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    atan2f.o(i.__hardfp_atan2f) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2f.o(i.__hardfp_atan2f) refers to fpclassifyf.o(i.__ARM_fpclassifyf) for __ARM_fpclassifyf
    atan2f.o(i.__hardfp_atan2f) refers to funder.o(i.__mathlib_flt_underflow) for __mathlib_flt_underflow
    atan2f.o(i.__hardfp_atan2f) refers to _rserrno.o(.text) for __set_errno
    atan2f.o(i.__hardfp_atan2f) refers to funder.o(i.__mathlib_flt_infnan2) for __mathlib_flt_infnan2
    atan2f.o(i.__softfp_atan2f) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2f.o(i.__softfp_atan2f) refers to atan2f.o(i.__hardfp_atan2f) for __hardfp_atan2f
    atan2f.o(i.atan2f) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2f.o(i.atan2f) refers to atan2f.o(i.__hardfp_atan2f) for __hardfp_atan2f
    atan2f_x.o(i.____hardfp_atan2f$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2f_x.o(i.____hardfp_atan2f$lsc) refers to fpclassifyf.o(i.__ARM_fpclassifyf) for __ARM_fpclassifyf
    atan2f_x.o(i.____hardfp_atan2f$lsc) refers to _rserrno.o(.text) for __set_errno
    atan2f_x.o(i.____hardfp_atan2f$lsc) refers to funder.o(i.__mathlib_flt_infnan2) for __mathlib_flt_infnan2
    atan2f_x.o(i.____softfp_atan2f$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2f_x.o(i.____softfp_atan2f$lsc) refers to atan2f_x.o(i.____hardfp_atan2f$lsc) for ____hardfp_atan2f$lsc
    atan2f_x.o(i.__atan2f$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2f_x.o(i.__atan2f$lsc) refers to atan2f_x.o(i.____hardfp_atan2f$lsc) for ____hardfp_atan2f$lsc
    sqrtf.o(i.__hardfp_sqrtf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrtf.o(i.__hardfp_sqrtf) refers to _rserrno.o(.text) for __set_errno
    sqrtf.o(i.__softfp_sqrtf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrtf.o(i.__softfp_sqrtf) refers to _rserrno.o(.text) for __set_errno
    sqrtf.o(i.sqrtf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrtf.o(i.sqrtf) refers to _rserrno.o(.text) for __set_errno
    sqrtf_x.o(i.____hardfp_sqrtf$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrtf_x.o(i.____hardfp_sqrtf$lsc) refers to _rserrno.o(.text) for __set_errno
    sqrtf_x.o(i.____softfp_sqrtf$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrtf_x.o(i.____softfp_sqrtf$lsc) refers to _rserrno.o(.text) for __set_errno
    sqrtf_x.o(i.__sqrtf$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrtf_x.o(i.__sqrtf$lsc) refers to _rserrno.o(.text) for __set_errno
    roundf.o(i.__hardfp_roundf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    roundf.o(i.__hardfp_roundf) refers to frnd.o(x$fpl$frnd) for _frnd
    roundf.o(i.roundf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    roundf.o(i.roundf) refers to roundf.o(i.__hardfp_roundf) for __hardfp_roundf
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for __rt_entry_li
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for __rt_entry_main
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000C) for __rt_entry_postli_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000009) for __rt_entry_postsh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000002) for __rt_entry_presh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for __rt_entry_sh
    aeabi_ldiv0_sigfpe.o(.text) refers to rt_div0.o(.text) for __rt_div0
    _rserrno.o(.text) refers to rt_errno_addr_intlibspace.o(.text) for __aeabi_errno_addr
    __printf.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    _printf_str.o(.text) refers (Special) to _printf_char.o(.text) for _printf_cs_common
    _printf_str.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_str.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_dec.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_signed
    _printf_dec.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_dec.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_char_common.o(.text) refers to __printf_flags_ss_wp.o(.text) for __printf
    _printf_wctomb.o(.text) refers (Special) to _printf_wchar.o(.text) for _printf_lcs_common
    _printf_wctomb.o(.text) refers to _wcrtomb.o(.text) for _wcrtomb
    _printf_wctomb.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_wctomb.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_wctomb.o(.text) refers to _printf_wctomb.o(.constdata) for .constdata
    _printf_wctomb.o(.constdata) refers (Special) to _printf_wchar.o(.text) for _printf_lcs_common
    _printf_longlong_dec.o(.text) refers to lludiv10.o(.text) for _ll_udiv10
    _printf_longlong_dec.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_int.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_oct_int.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_int_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_int_ll.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll.o(.text) refers to _printf_hex_ll.o(.constdata) for .constdata
    _printf_hex_int.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int.o(.text) refers to _printf_hex_int.o(.constdata) for .constdata
    _printf_hex_int_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ll.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int_ll.o(.text) refers to _printf_hex_int_ll.o(.constdata) for .constdata
    _printf_hex_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ptr.o(.text) refers to _printf_hex_ptr.o(.constdata) for .constdata
    _printf_hex_int_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ptr.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int_ptr.o(.text) refers to _printf_hex_int_ptr.o(.constdata) for .constdata
    _printf_hex_ll_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll_ptr.o(.text) refers to _printf_hex_ll_ptr.o(.constdata) for .constdata
    _printf_hex_int_ll_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ll_ptr.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int_ll_ptr.o(.text) refers to _printf_hex_int_ll_ptr.o(.constdata) for .constdata
    __printf_flags.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags.o(.text) refers to __printf_flags.o(.constdata) for .constdata
    __printf_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to __printf_flags_ss.o(.constdata) for .constdata
    __printf_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_flags_wp.o(.constdata) for .constdata
    __printf_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_flags_ss_wp.o(.constdata) for .constdata
    _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) refers (Weak) to _printf_char.o(.text) for _printf_char
    _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) refers (Weak) to _printf_char.o(.text) for _printf_string
    _printf_n.o(.ARM.Collect$$_printf_percent$$00000001) refers (Weak) to _printf_charcount.o(.text) for _printf_charcount
    _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) refers (Weak) to _printf_hex_int_ll_ptr.o(.text) for _printf_int_hex
    _printf_p.o(.ARM.Collect$$_printf_percent$$00000002) refers (Weak) to _printf_hex_int_ll_ptr.o(.text) for _printf_hex_ptr
    _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B) refers (Weak) to _printf_oct_int_ll.o(.text) for _printf_int_oct
    _printf_i.o(.ARM.Collect$$_printf_percent$$00000008) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_e.o(.ARM.Collect$$_printf_percent$$00000004) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_g.o(.ARM.Collect$$_printf_percent$$00000005) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_a.o(.ARM.Collect$$_printf_percent$$00000006) refers (Weak) to printf2.o(x$fpl$printf2) for _printf_fp_hex
    _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) refers (Special) to _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017) for _printf_percent_end
    _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D) refers (Weak) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E) refers (Weak) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F) refers (Weak) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015) refers (Special) to _printf_l.o(.ARM.Collect$$_printf_percent$$00000012) for _printf_l
    _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015) refers (Weak) to _printf_wchar.o(.text) for _printf_wchar
    _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016) refers (Special) to _printf_l.o(.ARM.Collect$$_printf_percent$$00000012) for _printf_l
    _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016) refers (Weak) to _printf_wchar.o(.text) for _printf_wstring
    _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010) refers (Weak) to _printf_oct_int_ll.o(.text) for _printf_ll_oct
    _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011) refers (Weak) to _printf_hex_int_ll_ptr.o(.text) for _printf_ll_hex
    scanf_char.o(.text) refers to _scanf.o(.text) for __vfscanf
    scanf_char.o(.text) refers to isspace.o(.text) for isspace
    abort.o(.text) refers to defsig_abrt_outer.o(.text) for __rt_SIGABRT
    abort.o(.text) refers (Weak) to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    abort.o(.text) refers to sys_exit.o(.text) for _sys_exit
    assert_puts.o(.text) refers to sys_wrch.o(.text) for _ttywrch
    dnaninf.o(x$fpl$dnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dretinf.o(x$fpl$dretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fnaninf.o(x$fpl$fnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fretinf.o(x$fpl$fretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    frnd.o(x$fpl$frnd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    frnd.o(x$fpl$frnd) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    printf1.o(x$fpl$printf1) refers to _printf_fp_dec.o(.text) for _printf_fp_dec_real
    printf2.o(x$fpl$printf2) refers to _printf_fp_hex.o(.text) for _printf_fp_hex_real
    printf2b.o(x$fpl$printf2) refers to _printf_fp_hex.o(.text) for _printf_fp_hex_real
    fpclassifyf.o(i.__ARM_fpclassifyf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sys_wrch.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    sys_exit.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_exit.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    __rtentry2.o(.ARM.Collect$$rtentry$$00000008) refers to boardinit2.o(.text) for _platform_post_stackheap_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) refers to libinit.o(.ARM.Collect$$libinit$$00000000) for __rt_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) refers to boardinit3.o(.text) for _platform_post_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to main.o(i.main) for main
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to exit.o(.text) for exit
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000001) for .ARM.Collect$$rtentry$$00000001
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000008) for .ARM.Collect$$rtentry$$00000008
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for .ARM.Collect$$rtentry$$0000000A
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) for .ARM.Collect$$rtentry$$0000000B
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for .ARM.Collect$$rtentry$$0000000D
    __rtentry4.o(.ARM.Collect$$rtentry$$00000004) refers to sys_stackheap_outer.o(.text) for __user_setup_stackheap
    __rtentry4.o(.ARM.exidx) refers to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for .ARM.Collect$$rtentry$$00000004
    rt_div0.o(.text) refers to defsig_fpe_outer.o(.text) for __rt_SIGFPE
    rt_errno_addr.o(.text) refers to rt_errno_addr.o(.bss) for __aeabi_errno_addr_data
    rt_errno_addr_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    isspace.o(.text) refers to rt_ctype_table.o(.text) for __rt_ctype_table
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_fp_dec.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_fp_dec.o(.text) refers (Special) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    _printf_fp_dec.o(.text) refers to bigflt0.o(.text) for _btod_etento
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_d2e) for _btod_d2e
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_ediv) for _btod_ediv
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_emul) for _btod_emul
    _printf_fp_dec.o(.text) refers to lludiv10.o(.text) for _ll_udiv10
    _printf_fp_dec.o(.text) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    _printf_fp_dec.o(.text) refers to _printf_fp_infnan.o(.text) for _printf_fp_infnan
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_dec.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_fp_hex.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_fp_hex.o(.text) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    _printf_fp_hex.o(.text) refers to _printf_fp_infnan.o(.text) for _printf_fp_infnan
    _printf_fp_hex.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_hex.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_hex.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_fp_hex.o(.text) refers to _printf_fp_hex.o(.constdata) for .constdata
    _printf_fp_hex.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_char.o(.text) refers (Weak) to _printf_str.o(.text) for _printf_str
    _printf_wchar.o(.text) refers (Weak) to _printf_wctomb.o(.text) for _printf_wctomb
    _scanf.o(.text) refers (Weak) to _scanf_int.o(.text) for _scanf_int
    _wcrtomb.o(.text) refers to rt_ctype_table.o(.text) for __rt_ctype_table
    defsig_abrt_outer.o(.text) refers to defsig_abrt_inner.o(.text) for __rt_SIGABRT_inner
    defsig_abrt_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_abrt_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    libspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    sys_stackheap_outer.o(.text) refers to libspace.o(.text) for __user_perproc_libspace
    sys_stackheap_outer.o(.text) refers to startup_stm32f407xx.o(.text) for __user_initial_stackheap
    rt_ctype_table.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    rt_ctype_table.o(.text) refers to lc_ctype_c.o(locale$$code) for _get_lc_ctype
    rt_raise.o(.text) refers to __raise.o(.text) for __raise
    rt_raise.o(.text) refers to sys_exit.o(.text) for _sys_exit
    rt_locale.o(.text) refers to rt_locale.o(.bss) for __rt_locale_data
    rt_locale_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    _printf_fp_infnan.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_infnan.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    bigflt0.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    bigflt0.o(.text) refers to btod.o(CL$$btod_emul) for _btod_emul
    bigflt0.o(.text) refers to btod.o(CL$$btod_ediv) for _btod_ediv
    bigflt0.o(.text) refers to bigflt0.o(.constdata) for .constdata
    bigflt0.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e) refers to btod.o(CL$$btod_d2e_norm_op1) for _d2e_norm_op1
    btod.o(CL$$btod_d2e_norm_op1) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e_norm_op1) refers to btod.o(CL$$btod_d2e_denorm_low) for _d2e_denorm_low
    btod.o(CL$$btod_d2e_denorm_low) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emul) refers to btod.o(CL$$btod_mult_common) for __btod_mult_common
    btod.o(CL$$btod_emul) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_ediv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_ediv) refers to btod.o(CL$$btod_div_common) for __btod_div_common
    btod.o(CL$$btod_ediv) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_emuld) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emuld) refers to btod.o(CL$$btod_mult_common) for __btod_mult_common
    btod.o(CL$$btod_emuld) refers to btod.o(CL$$btod_e2d) for _e2d
    btod.o(CL$$btod_edivd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_edivd) refers to btod.o(CL$$btod_div_common) for __btod_div_common
    btod.o(CL$$btod_edivd) refers to btod.o(CL$$btod_e2d) for _e2d
    btod.o(CL$$btod_e2e) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_e2d) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_e2d) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_mult_common) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_div_common) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    lc_numeric_c.o(locale$$data) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000016) for __rt_lib_init_lc_numeric_2
    lc_numeric_c.o(locale$$code) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000016) for __rt_lib_init_lc_numeric_2
    lc_numeric_c.o(locale$$code) refers to strcmpv7m.o(.text) for strcmp
    lc_numeric_c.o(locale$$code) refers to lc_numeric_c.o(locale$$data) for __lcnum_c_name
    exit.o(.text) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for __rt_exit
    defsig_exit.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_abrt_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_fpe_outer.o(.text) refers to defsig_fpe_inner.o(.text) for __rt_SIGFPE_inner
    defsig_fpe_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_fpe_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002E) for __rt_lib_init_alloca_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002C) for __rt_lib_init_argv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001B) for __rt_lib_init_atexit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000021) for __rt_lib_init_clock_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000032) for __rt_lib_init_cpp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000030) for __rt_lib_init_exceptions_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000001) for __rt_lib_init_fp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001F) for __rt_lib_init_fp_trap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000023) for __rt_lib_init_getenv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000A) for __rt_lib_init_heap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000011) for __rt_lib_init_lc_collate_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000013) for __rt_lib_init_lc_ctype_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000015) for __rt_lib_init_lc_monetary_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000017) for __rt_lib_init_lc_numeric_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000019) for __rt_lib_init_lc_time_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000004) for __rt_lib_init_preinit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000E) for __rt_lib_init_rand_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000033) for __rt_lib_init_return
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001D) for __rt_lib_init_signal_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000025) for __rt_lib_init_stdio_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000C) for __rt_lib_init_user_alloc_1
    istatus.o(x$fpl$ieeestatus) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fpclassify.o(i.__ARM_fpclassify) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for .ARM.Collect$$rtexit$$00000000
    lc_ctype_c.o(locale$$data) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000012) for __rt_lib_init_lc_ctype_2
    lc_ctype_c.o(locale$$code) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000012) for __rt_lib_init_lc_ctype_2
    lc_ctype_c.o(locale$$code) refers to strcmpv7m.o(.text) for strcmp
    lc_ctype_c.o(locale$$code) refers to lc_ctype_c.o(locale$$data) for __lcctype_c_name
    __raise.o(.text) refers to defsig.o(CL$$defsig) for __default_signal_handler
    defsig_general.o(.text) refers to sys_wrch.o(.text) for _ttywrch
    defsig_fpe_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    libinit2.o(.ARM.Collect$$libinit$$00000001) refers to fpinit.o(x$fpl$fpinit) for _fp_init
    libinit2.o(.ARM.Collect$$libinit$$0000000F) refers (Weak) to rt_locale_intlibspace.o(.text) for __rt_locale
    libinit2.o(.ARM.Collect$$libinit$$00000010) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers (Weak) to lc_ctype_c.o(locale$$code) for _get_lc_ctype
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers (Weak) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000026) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    libinit2.o(.ARM.Collect$$libinit$$00000027) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    rtexit2.o(.ARM.Collect$$rtexit$$00000003) refers to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    rtexit2.o(.ARM.Collect$$rtexit$$00000004) refers to sys_exit.o(.text) for _sys_exit
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000001) for .ARM.Collect$$rtexit$$00000001
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for .ARM.Collect$$rtexit$$00000003
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for .ARM.Collect$$rtexit$$00000004
    defsig.o(CL$$defsig) refers to defsig_abrt_inner.o(.text) for __rt_SIGABRT_inner
    defsig.o(CL$$defsig) refers to defsig_fpe_inner.o(.text) for __rt_SIGFPE_inner
    defsig.o(CL$$defsig) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    argv_veneer.o(.emb_text) refers to no_argv.o(.text) for __ARM_get_argv
    defsig_rtred_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtmem_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_stak_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_pvfn_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_cppl_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_segv_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_other.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    _get_argv_nomalloc.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv_nomalloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv_nomalloc.o(.text) refers to sys_command.o(.text) for _sys_command_string
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000004) for __rt_lib_shutdown_cpp_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000002) for __rt_lib_shutdown_fini_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000009) for __rt_lib_shutdown_fp_trap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000011) for __rt_lib_shutdown_heap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000012) for __rt_lib_shutdown_return
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C) for __rt_lib_shutdown_signal_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000006) for __rt_lib_shutdown_stdio_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E) for __rt_lib_shutdown_user_alloc_1
    sys_command.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig_rtmem_outer.o(.text) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_rtmem_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtmem_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise


==============================================================================

Removing Unused input sections from the image.

    Removing main.o(.rev16_text), (4 bytes).
    Removing main.o(.revsh_text), (4 bytes).
    Removing main.o(.rrx_text), (6 bytes).
    Removing gpio.o(.rev16_text), (4 bytes).
    Removing gpio.o(.revsh_text), (4 bytes).
    Removing gpio.o(.rrx_text), (6 bytes).
    Removing dma.o(.rev16_text), (4 bytes).
    Removing dma.o(.revsh_text), (4 bytes).
    Removing dma.o(.rrx_text), (6 bytes).
    Removing i2c.o(.rev16_text), (4 bytes).
    Removing i2c.o(.revsh_text), (4 bytes).
    Removing i2c.o(.rrx_text), (6 bytes).
    Removing i2c.o(i.HAL_I2C_MspDeInit), (100 bytes).
    Removing tim.o(.rev16_text), (4 bytes).
    Removing tim.o(.revsh_text), (4 bytes).
    Removing tim.o(.rrx_text), (6 bytes).
    Removing tim.o(i.HAL_TIM_Base_MspDeInit), (68 bytes).
    Removing tim.o(i.HAL_TIM_Encoder_MspDeInit), (88 bytes).
    Removing usart.o(.rev16_text), (4 bytes).
    Removing usart.o(.revsh_text), (4 bytes).
    Removing usart.o(.rrx_text), (6 bytes).
    Removing usart.o(i.HAL_UART_MspDeInit), (128 bytes).
    Removing stm32f4xx_it.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_it.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_it.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_msp.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_msp.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_msp.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_i2c.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_i2c.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_i2c.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_AbortCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_AddrCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_DeInit), (60 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_DisableListen_IT), (70 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_ER_IRQHandler), (212 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler), (474 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_EnableListen_IT), (70 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_ErrorCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_GetError), (6 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_GetMode), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_GetState), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_IsDeviceReady), (440 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_MasterTxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Abort_IT), (126 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive), (780 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA), (460 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_IT), (244 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA), (676 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_IT), (364 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA), (548 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_IT), (288 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit), (368 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA), (460 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_IT), (232 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_MemRxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_MemTxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA), (612 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_IT), (264 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA), (540 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_IT), (244 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_MspInit), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive), (384 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA), (308 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_IT), (152 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA), (460 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_IT), (158 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA), (460 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_IT), (158 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit), (404 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA), (308 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_IT), (152 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_ConvertOtherXferOptions), (28 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_DMAAbort), (248 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_DMAError), (80 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt), (318 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_Flush_DR), (18 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_ITError), (428 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_BTF), (304 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_RXNE), (260 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_MasterRequestRead), (312 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_MasterRequestWrite), (192 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_BTF), (160 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_TXE), (216 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_Master_ADD10), (42 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_Master_ADDR), (616 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_Master_SB), (158 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_MemoryTransmit_TXE_BTF), (198 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_SlaveReceive_BTF), (26 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_SlaveReceive_RXNE), (70 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_SlaveTransmit_BTF), (26 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_SlaveTransmit_TXE), (70 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_Slave_ADDR), (112 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_Slave_AF), (168 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_Slave_STOPF), (396 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout), (98 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_WaitOnSTOPRequestThroughIT), (80 bytes).
    Removing stm32f4xx_hal_i2c_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_i2c_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_i2c_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_rcc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_rcc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_rcc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_CSSCallback), (2 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_DeInit), (4 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_DisableCSS), (12 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_EnableCSS), (12 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_GetClockConfig), (68 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_GetOscConfig), (244 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_MCOConfig), (192 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler), (32 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_DisablePLLI2S), (56 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnablePLLI2S), (112 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKConfig), (56 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq), (148 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig), (416 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit), (384 bytes).
    Removing stm32f4xx_hal_flash.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_flash.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_flash.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_Program_Byte), (36 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_Program_DoubleWord), (48 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_Program_HalfWord), (40 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_Program_Word), (40 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_SetErrorCode), (152 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation), (100 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_EndOfOperationCallback), (2 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_GetError), (12 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler), (260 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_Lock), (20 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_OB_Launch), (28 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_OB_Lock), (20 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_OB_Unlock), (44 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_OperationErrorCallback), (2 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_Program), (144 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT), (112 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_Unlock), (52 bytes).
    Removing stm32f4xx_hal_flash.o(.bss), (32 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_Erase_Sector), (92 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_FlushCaches), (120 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_MassErase), (48 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_OB_BOR_LevelConfig), (32 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP), (40 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP), (40 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_OB_GetBOR), (16 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_OB_GetRDP), (36 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_OB_GetUser), (16 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_OB_GetWRP), (12 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_OB_RDP_LevelConfig), (32 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_OB_UserConfig), (56 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase), (172 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT), (100 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig), (34 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram), (148 bytes).
    Removing stm32f4xx_hal_flash_ramfunc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_flash_ramfunc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_flash_ramfunc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_gpio.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_gpio.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_gpio.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit), (360 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_LockPin), (46 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_TogglePin), (20 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_dma_ex.o(i.DMA_MultiBufferSetConfig), (32 bytes).
    Removing stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_ChangeMemory), (18 bytes).
    Removing stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart), (124 bytes).
    Removing stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart_IT), (4056 bytes).
    Removing stm32f4xx_hal_dma.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_dma.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_dma.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_DeInit), (112 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_GetError), (6 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_GetState), (8 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_PollForTransfer), (346 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_RegisterCallback), (96 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_Start), (102 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_UnRegisterCallback), (124 bytes).
    Removing stm32f4xx_hal_pwr.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_pwr.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_pwr.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_ConfigPVD), (180 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DeInit), (28 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisableBkUpAccess), (28 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisablePVD), (12 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisableSEVOnPend), (20 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisableSleepOnExit), (20 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisableWakeUpPin), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnableBkUpAccess), (28 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnablePVD), (12 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnableSEVOnPend), (20 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnableSleepOnExit), (20 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnableWakeUpPin), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnterSLEEPMode), (36 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnterSTANDBYMode), (40 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnterSTOPMode), (68 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_PVDCallback), (2 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_PVD_IRQHandler), (32 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_ControlVoltageScaling), (120 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableBkUpReg), (60 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableFlashPowerDown), (12 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableBkUpReg), (60 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableFlashPowerDown), (12 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_GetVoltageRange), (16 bytes).
    Removing stm32f4xx_hal_cortex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_cortex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_cortex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_CORTEX_ClearEvent), (6 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_ConfigRegion), (92 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_Disable), (32 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_DisableRegion), (28 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_Enable), (40 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_EnableRegion), (28 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_ClearPendingIRQ), (36 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ), (44 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetActive), (48 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetPendingIRQ), (48 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetPriority), (148 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetPriorityGrouping), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPendingIRQ), (36 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_SystemReset), (44 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_CLKSourceConfig), (40 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Callback), (2 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_DisableDBGSleepMode), (20 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_DisableDBGStandbyMode), (20 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_DisableDBGStopMode), (20 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_EnableDBGSleepMode), (20 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_EnableDBGStandbyMode), (20 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_EnableDBGStopMode), (20 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DeInit), (80 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DisableCompensationCell), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_EnableCompensationCell), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetDEVID), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetHalVersion), (8 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetREVID), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetTickFreq), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetTickPrio), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetUIDw0), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetUIDw1), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetUIDw2), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal.o(i.HAL_MspInit), (2 bytes).
    Removing stm32f4xx_hal.o(i.HAL_ResumeTick), (18 bytes).
    Removing stm32f4xx_hal.o(i.HAL_SetTickFreq), (48 bytes).
    Removing stm32f4xx_hal.o(i.HAL_SuspendTick), (18 bytes).
    Removing stm32f4xx_hal_exti.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_exti.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_exti.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_ClearConfigLine), (120 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_ClearPending), (28 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_GenerateSWI), (24 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_GetConfigLine), (156 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_GetHandle), (14 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_GetPending), (36 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_IRQHandler), (44 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_RegisterCallback), (20 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_SetConfigLine), (240 bytes).
    Removing stm32f4xx_hal_tim.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_tim.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_tim.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_DeInit), (120 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_GetState), (8 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start), (156 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA), (248 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Stop), (50 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Stop_DMA), (70 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Stop_IT), (62 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigOCrefClear), (278 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigTI1Input), (22 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurstState), (8 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart), (476 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart), (476 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStart), (42 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStop), (140 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStart), (42 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStop), (140 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_DeInit), (96 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_GetState), (8 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA), (556 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_IT), (252 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop), (214 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA), (280 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_IT), (256 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_ErrorCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_GenerateEvent), (54 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_GetActiveChannel), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_GetChannelState), (38 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel), (222 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_DeInit), (120 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_GetState), (8 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Init), (102 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start), (316 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA), (608 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_IT), (400 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop), (138 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA), (244 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop_IT), (222 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel), (112 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_DeInit), (120 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_GetState), (8 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Init), (102 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start), (268 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA), (588 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_IT), (352 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop), (168 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA), (276 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop_IT), (252 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel), (310 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_DeInit), (96 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_GetState), (8 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Init), (102 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start), (140 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start_IT), (164 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop), (160 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop_IT), (184 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_DeInit), (120 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_GetState), (8 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA), (588 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_IT), (352 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop), (168 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA), (276 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_IT), (252 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PeriodElapsedCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PeriodElapsedHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_ReadCapturedValue), (50 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro), (108 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro_IT), (108 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt), (122 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt), (64 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt), (104 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt), (64 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMAError), (94 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt), (26 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt), (14 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMATriggerCplt), (26 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMATriggerHalfCplt), (14 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig), (178 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_TI1_SetConfig), (144 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_TI2_SetConfig), (58 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_TI3_SetConfig), (56 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_TI4_SetConfig), (60 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent), (134 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA), (164 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_IT), (134 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_GetChannelNState), (38 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_DeInit), (96 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_GetState), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init), (234 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start), (216 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA), (280 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_IT), (228 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop), (78 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA), (84 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_IT), (90 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start), (244 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA), (504 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_IT), (324 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop), (136 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA), (220 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_IT), (230 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start), (118 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT), (142 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop), (138 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT), (162 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start), (244 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA), (504 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_IT), (324 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop), (136 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA), (220 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_IT), (230 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_RemapConfig), (42 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt), (20 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt), (20 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd), (34 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt), (82 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN), (68 bytes).
    Removing stm32f4xx_hal_uart.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_uart.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_uart.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_EnableReceiver), (68 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_EnableTransmitter), (68 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_Init), (122 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_LIN_Init), (146 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_LIN_SendBreak), (80 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_EnterMuteMode), (82 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_ExitMuteMode), (82 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_Init), (158 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UARTEx_GetRxEventType), (6 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle), (296 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_IT), (114 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Abort), (282 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive), (196 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT), (220 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit), (126 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT), (144 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT), (336 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_DMAPause), (166 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_DMAResume), (170 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_DeInit), (62 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_GetError), (6 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_GetState), (20 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_MspInit), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Receive), (194 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Receive_DMA), (44 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Receive_IT), (44 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA), (156 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_IT), (56 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_TxHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMARxAbortCallback), (56 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback), (28 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMATransmitCplt), (88 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMATxAbortCallback), (56 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMATxHalfCplt), (14 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback), (24 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_Start_Receive_IT), (64 bytes).
    Removing system_stm32f4xx.o(.rev16_text), (4 bytes).
    Removing system_stm32f4xx.o(.revsh_text), (4 bytes).
    Removing system_stm32f4xx.o(.rrx_text), (6 bytes).
    Removing system_stm32f4xx.o(i.SystemCoreClockUpdate), (180 bytes).
    Removing ringbuffer.o(i.rt_ringbuffer_getchar), (160 bytes).
    Removing ringbuffer.o(i.rt_ringbuffer_peek), (172 bytes).
    Removing ringbuffer.o(i.rt_ringbuffer_put_force), (304 bytes).
    Removing ringbuffer.o(i.rt_ringbuffer_putchar), (164 bytes).
    Removing ringbuffer.o(i.rt_ringbuffer_putchar_force), (200 bytes).
    Removing ringbuffer.o(i.rt_ringbuffer_reset), (112 bytes).
    Removing software_iic.o(.rev16_text), (4 bytes).
    Removing software_iic.o(.revsh_text), (4 bytes).
    Removing software_iic.o(.rrx_text), (6 bytes).
    Removing software_iic.o(i.IIC_Anolog_Normalize), (16 bytes).
    Removing software_iic.o(i.IIC_Get_Anolog), (28 bytes).
    Removing software_iic.o(i.IIC_Get_Offset), (32 bytes).
    Removing software_iic.o(i.IIC_Get_Single_Anolog), (26 bytes).
    Removing software_iic.o(i.IIC_ReadByte), (34 bytes).
    Removing software_iic.o(i.IIC_WriteByte), (70 bytes).
    Removing software_iic.o(i.IIC_WriteBytes), (88 bytes).
    Removing pid.o(i.pid_app_limit_integral), (40 bytes).
    Removing pid.o(i.pid_calculate_incremental), (38 bytes).
    Removing pid.o(i.pid_formula_incremental), (142 bytes).
    Removing pid.o(i.pid_set_limit), (6 bytes).
    Removing pid.o(i.pid_set_params), (14 bytes).
    Removing motor_driver.o(.rev16_text), (4 bytes).
    Removing motor_driver.o(.revsh_text), (4 bytes).
    Removing motor_driver.o(.rrx_text), (6 bytes).
    Removing motor_driver.o(i.Motor_Enable), (60 bytes).
    Removing motor_driver.o(i.Motor_GetState), (22 bytes).
    Removing motor_driver.o(i.Motor_SetDecayMode), (70 bytes).
    Removing motor_driver.o(i.Motor_Stop), (52 bytes).
    Removing oled.o(.rev16_text), (4 bytes).
    Removing oled.o(.revsh_text), (4 bytes).
    Removing oled.o(.rrx_text), (6 bytes).
    Removing oled.o(i.OLED_Allfill), (56 bytes).
    Removing oled.o(i.OLED_Display_Off), (22 bytes).
    Removing oled.o(i.OLED_Display_On), (22 bytes).
    Removing oled.o(i.OLED_Pow), (22 bytes).
    Removing oled.o(i.OLED_ShowFloat), (352 bytes).
    Removing oled.o(i.OLED_ShowHanzi), (100 bytes).
    Removing oled.o(i.OLED_ShowHzbig), (184 bytes).
    Removing oled.o(i.OLED_ShowNum), (136 bytes).
    Removing oled.o(i.OLED_ShowPic), (76 bytes).
    Removing encoder_driver.o(.rev16_text), (4 bytes).
    Removing encoder_driver.o(.revsh_text), (4 bytes).
    Removing encoder_driver.o(.rrx_text), (6 bytes).
    Removing mpu6050_hal.o(.rev16_text), (4 bytes).
    Removing mpu6050_hal.o(.revsh_text), (4 bytes).
    Removing mpu6050_hal.o(.rrx_text), (6 bytes).
    Removing mpu6050_hal.o(i.MPU6050_GetTemperature), (60 bytes).
    Removing led_driver.o(.rev16_text), (4 bytes).
    Removing led_driver.o(.revsh_text), (4 bytes).
    Removing led_driver.o(.rrx_text), (6 bytes).
    Removing key_driver.o(.rev16_text), (4 bytes).
    Removing key_driver.o(.revsh_text), (4 bytes).
    Removing key_driver.o(.rrx_text), (6 bytes).
    Removing usart_app.o(.rev16_text), (4 bytes).
    Removing usart_app.o(.revsh_text), (4 bytes).
    Removing usart_app.o(.rrx_text), (6 bytes).
    Removing led_app.o(.rev16_text), (4 bytes).
    Removing led_app.o(.revsh_text), (4 bytes).
    Removing led_app.o(.rrx_text), (6 bytes).
    Removing key_app.o(.rev16_text), (4 bytes).
    Removing key_app.o(.revsh_text), (4 bytes).
    Removing key_app.o(.rrx_text), (6 bytes).
    Removing oled_app.o(.rev16_text), (4 bytes).
    Removing oled_app.o(.revsh_text), (4 bytes).
    Removing oled_app.o(.rrx_text), (6 bytes).
    Removing motor_app.o(.rev16_text), (4 bytes).
    Removing motor_app.o(.revsh_text), (4 bytes).
    Removing motor_app.o(.rrx_text), (6 bytes).
    Removing motor_app.o(i.motor_break), (24 bytes).
    Removing encoder_app.o(.rev16_text), (4 bytes).
    Removing encoder_app.o(.revsh_text), (4 bytes).
    Removing encoder_app.o(.rrx_text), (6 bytes).
    Removing pid_app.o(.rev16_text), (4 bytes).
    Removing pid_app.o(.revsh_text), (4 bytes).
    Removing pid_app.o(.rrx_text), (6 bytes).
    Removing gray_app.o(.rev16_text), (4 bytes).
    Removing gray_app.o(.revsh_text), (4 bytes).
    Removing gray_app.o(.rrx_text), (6 bytes).
    Removing mpu6050_app.o(.rev16_text), (4 bytes).
    Removing mpu6050_app.o(.revsh_text), (4 bytes).
    Removing mpu6050_app.o(.rrx_text), (6 bytes).
    Removing mpu6050_app.o(i.calibrateGyro), (12 bytes).
    Removing mpu6050_app.o(i.dataAvailable), (24 bytes).
    Removing mpu6050_app.o(i.getAccelX), (12 bytes).
    Removing mpu6050_app.o(i.getAccelY), (12 bytes).
    Removing mpu6050_app.o(i.getAccelZ), (12 bytes).
    Removing mpu6050_app.o(i.getGyroX), (12 bytes).
    Removing mpu6050_app.o(i.getGyroY), (12 bytes).
    Removing mpu6050_app.o(i.getGyroZ), (12 bytes).
    Removing mpu6050_app.o(i.getTemperature), (12 bytes).
    Removing mpu6050_app.o(i.get_pitch), (12 bytes).
    Removing mpu6050_app.o(i.get_roll), (12 bytes).
    Removing mpu6050_app.o(i.softReset), (8 bytes).
    Removing my_scheduler.o(.rev16_text), (4 bytes).
    Removing my_scheduler.o(.revsh_text), (4 bytes).
    Removing my_scheduler.o(.rrx_text), (6 bytes).
    Removing my_timer.o(.rev16_text), (4 bytes).
    Removing my_timer.o(.revsh_text), (4 bytes).
    Removing my_timer.o(.rrx_text), (6 bytes).

563 unused section(s) (total 54054 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../Core/Src/dma.c                        0x00000000   Number         0  dma.o ABSOLUTE
    ../Core/Src/gpio.c                       0x00000000   Number         0  gpio.o ABSOLUTE
    ../Core/Src/i2c.c                        0x00000000   Number         0  i2c.o ABSOLUTE
    ../Core/Src/main.c                       0x00000000   Number         0  main.o ABSOLUTE
    ../Core/Src/stm32f4xx_hal_msp.c          0x00000000   Number         0  stm32f4xx_hal_msp.o ABSOLUTE
    ../Core/Src/stm32f4xx_it.c               0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    ../Core/Src/system_stm32f4xx.c           0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    ../Core/Src/tim.c                        0x00000000   Number         0  tim.o ABSOLUTE
    ../Core/Src/usart.c                      0x00000000   Number         0  usart.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.c 0x00000000   Number         0  stm32f4xx_hal.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.c 0x00000000   Number         0  stm32f4xx_hal_cortex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.c 0x00000000   Number         0  stm32f4xx_hal_dma.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.c 0x00000000   Number         0  stm32f4xx_hal_dma_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.c 0x00000000   Number         0  stm32f4xx_hal_exti.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.c 0x00000000   Number         0  stm32f4xx_hal_flash.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.c 0x00000000   Number         0  stm32f4xx_hal_flash_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.c 0x00000000   Number         0  stm32f4xx_hal_flash_ramfunc.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.c 0x00000000   Number         0  stm32f4xx_hal_gpio.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_i2c.c 0x00000000   Number         0  stm32f4xx_hal_i2c.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_i2c_ex.c 0x00000000   Number         0  stm32f4xx_hal_i2c_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.c 0x00000000   Number         0  stm32f4xx_hal_pwr.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.c 0x00000000   Number         0  stm32f4xx_hal_pwr_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.c 0x00000000   Number         0  stm32f4xx_hal_rcc.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.c 0x00000000   Number         0  stm32f4xx_hal_rcc_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.c 0x00000000   Number         0  stm32f4xx_hal_tim.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.c 0x00000000   Number         0  stm32f4xx_hal_tim_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_uart.c 0x00000000   Number         0  stm32f4xx_hal_uart.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardshut.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit3.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit2.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit1.o ABSOLUTE
    ../clib/angel/dczerorl2.s                0x00000000   Number         0  __dczerorl2.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_zi.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry4.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry2.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_ctype_table.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_raise.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  aeabi_ldiv0.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  aeabi_ldiv0_sigfpe.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_div0.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr_intlibspace.o ABSOLUTE
    ../clib/angel/scatter.s                  0x00000000   Number         0  __scatter.o ABSOLUTE
    ../clib/angel/startup.s                  0x00000000   Number         0  __main.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  libspace.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  indicate_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  sys_stackheap_outer.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_exit.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  no_argv.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv_nomalloc.o ABSOLUTE
    ../clib/assert.c                         0x00000000   Number         0  assert_puts.o ABSOLUTE
    ../clib/assert.c                         0x00000000   Number         0  assert.o ABSOLUTE
    ../clib/bigflt.c                         0x00000000   Number         0  bigflt0.o ABSOLUTE
    ../clib/btod.s                           0x00000000   Number         0  btod.o ABSOLUTE
    ../clib/ctype.c                          0x00000000   Number         0  isspace.o ABSOLUTE
    ../clib/fenv.c                           0x00000000   Number         0  _rserrno.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hrguard.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxi.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown2.o ABSOLUTE
    ../clib/locale.c                         0x00000000   Number         0  _wcrtomb.o ABSOLUTE
    ../clib/locale.s                         0x00000000   Number         0  lc_numeric_c.o ABSOLUTE
    ../clib/locale.s                         0x00000000   Number         0  lc_ctype_c.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  lludiv10.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  lludivv7m.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memclr_w.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  strcmpv7m.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memcpy_w.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memcpy_v6.o ABSOLUTE
    ../clib/misc.s                           0x00000000   Number         0  printf_stubs.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _snputc.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_pad.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_nopercent.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_infnan.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_longlong_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ll_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_intcommon.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_oct_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_oct_int.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_oct_int_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_wctomb.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_hex.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _sputc.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_common.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_wchar.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ll_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_charcount.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  vsnprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_str.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_truncate.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_lli.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_lld.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_llx.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_llu.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_ll.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_lc.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_ls.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_llo.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_n.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent_end.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_g.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_c.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_s.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_x.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_l.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_p.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_o.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_i.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_d.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_u.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_f.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_e.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_a.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _scanf_int.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _scanf.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _sgetc.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  scanf_char.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  __0sscanf.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _chval.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_pvfn_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_other.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_segv_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_cppl_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  __raise.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_stak_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_general.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_exit.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_outer.o ABSOLUTE
    ../clib/signal.s                         0x00000000   Number         0  defsig.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  exit.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  abort.o ABSOLUTE
    ../fplib/d2f.s                           0x00000000   Number         0  d2f.o ABSOLUTE
    ../fplib/ddiv.s                          0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/dnaninf.s                       0x00000000   Number         0  dnaninf.o ABSOLUTE
    ../fplib/dretinf.s                       0x00000000   Number         0  dretinf.o ABSOLUTE
    ../fplib/f2d.s                           0x00000000   Number         0  f2d.o ABSOLUTE
    ../fplib/fnaninf.s                       0x00000000   Number         0  fnaninf.o ABSOLUTE
    ../fplib/fpinit.s                        0x00000000   Number         0  fpinit.o ABSOLUTE
    ../fplib/fretinf.s                       0x00000000   Number         0  fretinf.o ABSOLUTE
    ../fplib/frnd.s                          0x00000000   Number         0  frnd.o ABSOLUTE
    ../fplib/istatus.s                       0x00000000   Number         0  istatus.o ABSOLUTE
    ../fplib/printf1.s                       0x00000000   Number         0  printf1.o ABSOLUTE
    ../fplib/printf2.s                       0x00000000   Number         0  printf2.o ABSOLUTE
    ../fplib/printf2a.s                      0x00000000   Number         0  printf2a.o ABSOLUTE
    ../fplib/printf2b.s                      0x00000000   Number         0  printf2b.o ABSOLUTE
    ../fplib/usenofp.s                       0x00000000   Number         0  usenofp.o ABSOLUTE
    ../mathlib/atan2f.c                      0x00000000   Number         0  atan2f_x.o ABSOLUTE
    ../mathlib/atan2f.c                      0x00000000   Number         0  atan2f.o ABSOLUTE
    ../mathlib/fpclassify.c                  0x00000000   Number         0  fpclassify.o ABSOLUTE
    ../mathlib/fpclassifyf.c                 0x00000000   Number         0  fpclassifyf.o ABSOLUTE
    ../mathlib/funder.c                      0x00000000   Number         0  funder.o ABSOLUTE
    ../mathlib/round.c                       0x00000000   Number         0  roundf.o ABSOLUTE
    ../mathlib/sqrtf.c                       0x00000000   Number         0  sqrtf_x.o ABSOLUTE
    ../mathlib/sqrtf.c                       0x00000000   Number         0  sqrtf.o ABSOLUTE
    ..\Core\Src\dma.c                        0x00000000   Number         0  dma.o ABSOLUTE
    ..\Core\Src\gpio.c                       0x00000000   Number         0  gpio.o ABSOLUTE
    ..\Core\Src\i2c.c                        0x00000000   Number         0  i2c.o ABSOLUTE
    ..\Core\Src\main.c                       0x00000000   Number         0  main.o ABSOLUTE
    ..\Core\Src\stm32f4xx_hal_msp.c          0x00000000   Number         0  stm32f4xx_hal_msp.o ABSOLUTE
    ..\Core\Src\stm32f4xx_it.c               0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    ..\Core\Src\system_stm32f4xx.c           0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    ..\Core\Src\tim.c                        0x00000000   Number         0  tim.o ABSOLUTE
    ..\Core\Src\usart.c                      0x00000000   Number         0  usart.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal.c 0x00000000   Number         0  stm32f4xx_hal.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_cortex.c 0x00000000   Number         0  stm32f4xx_hal_cortex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_dma.c 0x00000000   Number         0  stm32f4xx_hal_dma.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_dma_ex.c 0x00000000   Number         0  stm32f4xx_hal_dma_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_exti.c 0x00000000   Number         0  stm32f4xx_hal_exti.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_flash.c 0x00000000   Number         0  stm32f4xx_hal_flash.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_flash_ex.c 0x00000000   Number         0  stm32f4xx_hal_flash_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_flash_ramfunc.c 0x00000000   Number         0  stm32f4xx_hal_flash_ramfunc.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_gpio.c 0x00000000   Number         0  stm32f4xx_hal_gpio.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_i2c.c 0x00000000   Number         0  stm32f4xx_hal_i2c.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_i2c_ex.c 0x00000000   Number         0  stm32f4xx_hal_i2c_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_pwr.c 0x00000000   Number         0  stm32f4xx_hal_pwr.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_pwr_ex.c 0x00000000   Number         0  stm32f4xx_hal_pwr_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_rcc.c 0x00000000   Number         0  stm32f4xx_hal_rcc.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_rcc_ex.c 0x00000000   Number         0  stm32f4xx_hal_rcc_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_tim.c 0x00000000   Number         0  stm32f4xx_hal_tim.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_tim_ex.c 0x00000000   Number         0  stm32f4xx_hal_tim_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_uart.c 0x00000000   Number         0  stm32f4xx_hal_uart.o ABSOLUTE
    ..\User\App\encoder_app.c                0x00000000   Number         0  encoder_app.o ABSOLUTE
    ..\User\App\gray_app.c                   0x00000000   Number         0  gray_app.o ABSOLUTE
    ..\User\App\key_app.c                    0x00000000   Number         0  key_app.o ABSOLUTE
    ..\User\App\led_app.c                    0x00000000   Number         0  led_app.o ABSOLUTE
    ..\User\App\motor_app.c                  0x00000000   Number         0  motor_app.o ABSOLUTE
    ..\User\App\mpu6050_app.c                0x00000000   Number         0  mpu6050_app.o ABSOLUTE
    ..\User\App\oled_app.c                   0x00000000   Number         0  oled_app.o ABSOLUTE
    ..\User\App\pid_app.c                    0x00000000   Number         0  pid_app.o ABSOLUTE
    ..\User\App\usart_app.c                  0x00000000   Number         0  usart_app.o ABSOLUTE
    ..\User\Driver\key_driver.c              0x00000000   Number         0  key_driver.o ABSOLUTE
    ..\User\Driver\led_driver.c              0x00000000   Number         0  led_driver.o ABSOLUTE
    ..\User\Module\encoder\encoder_driver.c  0x00000000   Number         0  encoder_driver.o ABSOLUTE
    ..\User\Module\grayscale\hardware_iic.c  0x00000000   Number         0  hardware_iic.o ABSOLUTE
    ..\User\Module\grayscale\software_iic.c  0x00000000   Number         0  software_iic.o ABSOLUTE
    ..\User\Module\motor\motor_driver.c      0x00000000   Number         0  motor_driver.o ABSOLUTE
    ..\User\Module\mpu6050\mpu6050_hal.c     0x00000000   Number         0  mpu6050_hal.o ABSOLUTE
    ..\User\Module\oled\oled.c               0x00000000   Number         0  oled.o ABSOLUTE
    ..\User\Module\pid\pid.c                 0x00000000   Number         0  pid.o ABSOLUTE
    ..\User\Module\ringbuffer\ringbuffer.c   0x00000000   Number         0  ringbuffer.o ABSOLUTE
    ..\User\my_scheduler.c                   0x00000000   Number         0  my_scheduler.o ABSOLUTE
    ..\User\my_timer.c                       0x00000000   Number         0  my_timer.o ABSOLUTE
    ..\\User\\App\\encoder_app.c             0x00000000   Number         0  encoder_app.o ABSOLUTE
    ..\\User\\App\\gray_app.c                0x00000000   Number         0  gray_app.o ABSOLUTE
    ..\\User\\App\\key_app.c                 0x00000000   Number         0  key_app.o ABSOLUTE
    ..\\User\\App\\led_app.c                 0x00000000   Number         0  led_app.o ABSOLUTE
    ..\\User\\App\\motor_app.c               0x00000000   Number         0  motor_app.o ABSOLUTE
    ..\\User\\App\\mpu6050_app.c             0x00000000   Number         0  mpu6050_app.o ABSOLUTE
    ..\\User\\App\\oled_app.c                0x00000000   Number         0  oled_app.o ABSOLUTE
    ..\\User\\App\\pid_app.c                 0x00000000   Number         0  pid_app.o ABSOLUTE
    ..\\User\\App\\usart_app.c               0x00000000   Number         0  usart_app.o ABSOLUTE
    ..\\User\\Driver\\key_driver.c           0x00000000   Number         0  key_driver.o ABSOLUTE
    ..\\User\\Driver\\led_driver.c           0x00000000   Number         0  led_driver.o ABSOLUTE
    ..\\User\\Module\\encoder\\encoder_driver.c 0x00000000   Number         0  encoder_driver.o ABSOLUTE
    ..\\User\\Module\\grayscale\\software_iic.c 0x00000000   Number         0  software_iic.o ABSOLUTE
    ..\\User\\Module\\motor\\motor_driver.c  0x00000000   Number         0  motor_driver.o ABSOLUTE
    ..\\User\\Module\\mpu6050\\mpu6050_hal.c 0x00000000   Number         0  mpu6050_hal.o ABSOLUTE
    ..\\User\\Module\\oled\\oled.c           0x00000000   Number         0  oled.o ABSOLUTE
    ..\\User\\my_scheduler.c                 0x00000000   Number         0  my_scheduler.o ABSOLUTE
    ..\\User\\my_timer.c                     0x00000000   Number         0  my_timer.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    startup_stm32f407xx.s                    0x00000000   Number         0  startup_stm32f407xx.o ABSOLUTE
    RESET                                    0x08000000   Section      392  startup_stm32f407xx.o(RESET)
    !!!main                                  0x08000188   Section        8  __main.o(!!!main)
    !!!scatter                               0x08000190   Section       52  __scatter.o(!!!scatter)
    !!dczerorl2                              0x080001c4   Section       90  __dczerorl2.o(!!dczerorl2)
    !!handler_zi                             0x08000220   Section       28  __scatter_zi.o(!!handler_zi)
    .ARM.Collect$$_printf_percent$$00000000  0x0800023c   Section        0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    .ARM.Collect$$_printf_percent$$00000001  0x0800023c   Section        6  _printf_n.o(.ARM.Collect$$_printf_percent$$00000001)
    .ARM.Collect$$_printf_percent$$00000002  0x08000242   Section        6  _printf_p.o(.ARM.Collect$$_printf_percent$$00000002)
    .ARM.Collect$$_printf_percent$$00000003  0x08000248   Section        6  _printf_f.o(.ARM.Collect$$_printf_percent$$00000003)
    .ARM.Collect$$_printf_percent$$00000004  0x0800024e   Section        6  _printf_e.o(.ARM.Collect$$_printf_percent$$00000004)
    .ARM.Collect$$_printf_percent$$00000005  0x08000254   Section        6  _printf_g.o(.ARM.Collect$$_printf_percent$$00000005)
    .ARM.Collect$$_printf_percent$$00000006  0x0800025a   Section        6  _printf_a.o(.ARM.Collect$$_printf_percent$$00000006)
    .ARM.Collect$$_printf_percent$$00000007  0x08000260   Section       10  _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007)
    .ARM.Collect$$_printf_percent$$00000008  0x0800026a   Section        6  _printf_i.o(.ARM.Collect$$_printf_percent$$00000008)
    .ARM.Collect$$_printf_percent$$00000009  0x08000270   Section        6  _printf_d.o(.ARM.Collect$$_printf_percent$$00000009)
    .ARM.Collect$$_printf_percent$$0000000A  0x08000276   Section        6  _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A)
    .ARM.Collect$$_printf_percent$$0000000B  0x0800027c   Section        6  _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B)
    .ARM.Collect$$_printf_percent$$0000000C  0x08000282   Section        6  _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C)
    .ARM.Collect$$_printf_percent$$0000000D  0x08000288   Section        6  _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D)
    .ARM.Collect$$_printf_percent$$0000000E  0x0800028e   Section        6  _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E)
    .ARM.Collect$$_printf_percent$$0000000F  0x08000294   Section        6  _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F)
    .ARM.Collect$$_printf_percent$$00000010  0x0800029a   Section        6  _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010)
    .ARM.Collect$$_printf_percent$$00000011  0x080002a0   Section        6  _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011)
    .ARM.Collect$$_printf_percent$$00000012  0x080002a6   Section       10  _printf_l.o(.ARM.Collect$$_printf_percent$$00000012)
    .ARM.Collect$$_printf_percent$$00000013  0x080002b0   Section        6  _printf_c.o(.ARM.Collect$$_printf_percent$$00000013)
    .ARM.Collect$$_printf_percent$$00000014  0x080002b6   Section        6  _printf_s.o(.ARM.Collect$$_printf_percent$$00000014)
    .ARM.Collect$$_printf_percent$$00000015  0x080002bc   Section        6  _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015)
    .ARM.Collect$$_printf_percent$$00000016  0x080002c2   Section        6  _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016)
    .ARM.Collect$$_printf_percent$$00000017  0x080002c8   Section        4  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    .ARM.Collect$$libinit$$00000000          0x080002cc   Section        2  libinit.o(.ARM.Collect$$libinit$$00000000)
    .ARM.Collect$$libinit$$00000001          0x080002ce   Section        4  libinit2.o(.ARM.Collect$$libinit$$00000001)
    .ARM.Collect$$libinit$$00000004          0x080002d2   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    .ARM.Collect$$libinit$$0000000A          0x080002d2   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    .ARM.Collect$$libinit$$0000000C          0x080002d2   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    .ARM.Collect$$libinit$$0000000E          0x080002d2   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    .ARM.Collect$$libinit$$0000000F          0x080002d2   Section        6  libinit2.o(.ARM.Collect$$libinit$$0000000F)
    .ARM.Collect$$libinit$$00000011          0x080002d8   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    .ARM.Collect$$libinit$$00000012          0x080002d8   Section       12  libinit2.o(.ARM.Collect$$libinit$$00000012)
    .ARM.Collect$$libinit$$00000013          0x080002e4   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    .ARM.Collect$$libinit$$00000015          0x080002e4   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    .ARM.Collect$$libinit$$00000016          0x080002e4   Section       10  libinit2.o(.ARM.Collect$$libinit$$00000016)
    .ARM.Collect$$libinit$$00000017          0x080002ee   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    .ARM.Collect$$libinit$$00000019          0x080002ee   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    .ARM.Collect$$libinit$$0000001B          0x080002ee   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    .ARM.Collect$$libinit$$0000001D          0x080002ee   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    .ARM.Collect$$libinit$$0000001F          0x080002ee   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    .ARM.Collect$$libinit$$00000021          0x080002ee   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    .ARM.Collect$$libinit$$00000023          0x080002ee   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    .ARM.Collect$$libinit$$00000025          0x080002ee   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    .ARM.Collect$$libinit$$0000002C          0x080002ee   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    .ARM.Collect$$libinit$$0000002E          0x080002ee   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    .ARM.Collect$$libinit$$00000030          0x080002ee   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    .ARM.Collect$$libinit$$00000032          0x080002ee   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    .ARM.Collect$$libinit$$00000033          0x080002ee   Section        2  libinit2.o(.ARM.Collect$$libinit$$00000033)
    .ARM.Collect$$libshutdown$$00000000      0x080002f0   Section        2  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    .ARM.Collect$$libshutdown$$00000002      0x080002f2   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    .ARM.Collect$$libshutdown$$00000004      0x080002f2   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    .ARM.Collect$$libshutdown$$00000006      0x080002f2   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000006)
    .ARM.Collect$$libshutdown$$00000009      0x080002f2   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000009)
    .ARM.Collect$$libshutdown$$0000000C      0x080002f2   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    .ARM.Collect$$libshutdown$$0000000E      0x080002f2   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E)
    .ARM.Collect$$libshutdown$$00000011      0x080002f2   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000011)
    .ARM.Collect$$libshutdown$$00000012      0x080002f2   Section        2  libshutdown2.o(.ARM.Collect$$libshutdown$$00000012)
    .ARM.Collect$$rtentry$$00000000          0x080002f4   Section        0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    .ARM.Collect$$rtentry$$00000002          0x080002f4   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    .ARM.Collect$$rtentry$$00000004          0x080002f4   Section        6  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    .ARM.Collect$$rtentry$$00000009          0x080002fa   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    .ARM.Collect$$rtentry$$0000000A          0x080002fa   Section        4  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    .ARM.Collect$$rtentry$$0000000C          0x080002fe   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    .ARM.Collect$$rtentry$$0000000D          0x080002fe   Section        8  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    .ARM.Collect$$rtexit$$00000000           0x08000306   Section        2  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    .ARM.Collect$$rtexit$$00000002           0x08000308   Section        0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    .ARM.Collect$$rtexit$$00000003           0x08000308   Section        4  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    .ARM.Collect$$rtexit$$00000004           0x0800030c   Section        6  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    .text                                    0x08000314   Section       64  startup_stm32f407xx.o(.text)
    $v0                                      0x08000314   Number         0  startup_stm32f407xx.o(.text)
    .text                                    0x08000354   Section      238  lludivv7m.o(.text)
    .text                                    0x08000444   Section        0  vsnprintf.o(.text)
    .text                                    0x08000478   Section        0  __0sscanf.o(.text)
    .text                                    0x080004b4   Section        0  _scanf_int.o(.text)
    .text                                    0x08000600   Section        0  assert.o(.text)
    .text                                    0x08000680   Section      138  rt_memcpy_v6.o(.text)
    .text                                    0x0800070a   Section       78  rt_memclr_w.o(.text)
    .text                                    0x08000758   Section        0  heapauxi.o(.text)
    .text                                    0x0800075e   Section        0  _rserrno.o(.text)
    .text                                    0x08000774   Section        0  _printf_pad.o(.text)
    .text                                    0x080007c2   Section        0  _printf_truncate.o(.text)
    .text                                    0x080007e6   Section        0  _printf_str.o(.text)
    .text                                    0x08000838   Section        0  _printf_dec.o(.text)
    .text                                    0x080008b0   Section        0  _printf_charcount.o(.text)
    .text                                    0x080008d8   Section        0  _printf_char_common.o(.text)
    _printf_input_char                       0x080008d9   Thumb Code    10  _printf_char_common.o(.text)
    .text                                    0x08000908   Section        0  _sputc.o(.text)
    .text                                    0x08000912   Section        0  _snputc.o(.text)
    .text                                    0x08000924   Section        0  _printf_wctomb.o(.text)
    .text                                    0x080009e0   Section        0  _printf_longlong_dec.o(.text)
    .text                                    0x08000a5c   Section        0  _printf_oct_int_ll.o(.text)
    _printf_longlong_oct_internal            0x08000a5d   Thumb Code     0  _printf_oct_int_ll.o(.text)
    .text                                    0x08000acc   Section        0  _printf_hex_int_ll_ptr.o(.text)
    _printf_hex_common                       0x08000acd   Thumb Code     0  _printf_hex_int_ll_ptr.o(.text)
    .text                                    0x08000b60   Section        0  __printf_flags_ss_wp.o(.text)
    .text                                    0x08000ce8   Section        0  _chval.o(.text)
    .text                                    0x08000d04   Section        0  scanf_char.o(.text)
    _scanf_char_input                        0x08000d05   Thumb Code    12  scanf_char.o(.text)
    .text                                    0x08000d30   Section        0  _sgetc.o(.text)
    .text                                    0x08000d70   Section        0  abort.o(.text)
    .text                                    0x08000d86   Section        0  assert_puts.o(.text)
    .text                                    0x08000d9a   Section      100  rt_memcpy_w.o(.text)
    .text                                    0x08000dfe   Section        0  sys_wrch.o(.text)
    .text                                    0x08000e0c   Section        0  sys_exit.o(.text)
    .text                                    0x08000e18   Section        8  rt_errno_addr_intlibspace.o(.text)
    .text                                    0x08000e20   Section      138  lludiv10.o(.text)
    .text                                    0x08000eaa   Section        0  isspace.o(.text)
    .text                                    0x08000ebc   Section        0  _printf_intcommon.o(.text)
    .text                                    0x08000f6e   Section        0  _printf_fp_dec.o(.text)
    _fp_digits                               0x08000f71   Thumb Code   432  _printf_fp_dec.o(.text)
    .text                                    0x0800138c   Section        0  _printf_fp_hex.o(.text)
    .text                                    0x08001688   Section        0  _printf_char.o(.text)
    .text                                    0x080016b4   Section        0  _printf_wchar.o(.text)
    .text                                    0x080016e0   Section        0  _scanf.o(.text)
    .text                                    0x08001a54   Section        0  _wcrtomb.o(.text)
    .text                                    0x08001a94   Section        0  defsig_abrt_outer.o(.text)
    .text                                    0x08001aa4   Section        8  libspace.o(.text)
    .text                                    0x08001aac   Section        2  use_no_semi.o(.text)
    .text                                    0x08001aae   Section       74  sys_stackheap_outer.o(.text)
    .text                                    0x08001aae   Section        0  indicate_semi.o(.text)
    .text                                    0x08001af8   Section       16  rt_ctype_table.o(.text)
    .text                                    0x08001b08   Section        8  rt_locale_intlibspace.o(.text)
    .text                                    0x08001b10   Section        0  _printf_fp_infnan.o(.text)
    .text                                    0x08001b90   Section        0  bigflt0.o(.text)
    .text                                    0x08001c74   Section        0  exit.o(.text)
    .text                                    0x08001c86   Section        0  defsig_exit.o(.text)
    .text                                    0x08001c90   Section        0  defsig_abrt_inner.o(.text)
    .text                                    0x08001cc0   Section        0  defsig_general.o(.text)
    .text                                    0x08001cf4   Section      128  strcmpv7m.o(.text)
    CL$$btod_d2e                             0x08001d74   Section       62  btod.o(CL$$btod_d2e)
    CL$$btod_d2e_denorm_low                  0x08001db2   Section       70  btod.o(CL$$btod_d2e_denorm_low)
    CL$$btod_d2e_norm_op1                    0x08001df8   Section       96  btod.o(CL$$btod_d2e_norm_op1)
    CL$$btod_div_common                      0x08001e58   Section      824  btod.o(CL$$btod_div_common)
    CL$$btod_e2e                             0x08002190   Section      220  btod.o(CL$$btod_e2e)
    CL$$btod_ediv                            0x0800226c   Section       42  btod.o(CL$$btod_ediv)
    CL$$btod_emul                            0x08002296   Section       42  btod.o(CL$$btod_emul)
    CL$$btod_mult_common                     0x080022c0   Section      580  btod.o(CL$$btod_mult_common)
    i.Angle_PID_control                      0x08002504   Section        0  pid_app.o(i.Angle_PID_control)
    i.BusFault_Handler                       0x08002598   Section        0  stm32f4xx_it.o(i.BusFault_Handler)
    i.Car_State_Update                       0x0800259c   Section        0  my_timer.o(i.Car_State_Update)
    i.DMA1_Stream0_IRQHandler                0x08002714   Section        0  stm32f4xx_it.o(i.DMA1_Stream0_IRQHandler)
    i.DMA2_Stream2_IRQHandler                0x08002724   Section        0  stm32f4xx_it.o(i.DMA2_Stream2_IRQHandler)
    i.DMA_CalcBaseAndBitshift                0x08002734   Section        0  stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift)
    DMA_CalcBaseAndBitshift                  0x08002735   Thumb Code    46  stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift)
    i.DMA_CheckFifoParam                     0x08002768   Section        0  stm32f4xx_hal_dma.o(i.DMA_CheckFifoParam)
    DMA_CheckFifoParam                       0x08002769   Thumb Code   170  stm32f4xx_hal_dma.o(i.DMA_CheckFifoParam)
    i.DMA_SetConfig                          0x08002812   Section        0  stm32f4xx_hal_dma.o(i.DMA_SetConfig)
    DMA_SetConfig                            0x08002813   Thumb Code    44  stm32f4xx_hal_dma.o(i.DMA_SetConfig)
    i.DRV8871_Control                        0x0800283e   Section        0  motor_driver.o(i.DRV8871_Control)
    DRV8871_Control                          0x0800283f   Thumb Code   224  motor_driver.o(i.DRV8871_Control)
    i.DebugMon_Handler                       0x0800291e   Section        0  stm32f4xx_it.o(i.DebugMon_Handler)
    i.Delay_us                               0x08002920   Section        0  software_iic.o(i.Delay_us)
    i.EXTI3_IRQHandler                       0x08002978   Section        0  stm32f4xx_it.o(i.EXTI3_IRQHandler)
    i.Encoder_Driver_Init                    0x08002984   Section        0  encoder_driver.o(i.Encoder_Driver_Init)
    i.Encoder_Driver_Update                  0x080029b4   Section        0  encoder_driver.o(i.Encoder_Driver_Update)
    i.Encoder_Init                           0x08002a44   Section        0  encoder_app.o(i.Encoder_Init)
    i.Encoder_Task                           0x08002a6c   Section        0  encoder_app.o(i.Encoder_Task)
    i.Error_Handler                          0x08002a84   Section        0  main.o(i.Error_Handler)
    i.Float_To_Speed1000                     0x08002a8a   Section        0  motor_driver.o(i.Float_To_Speed1000)
    Float_To_Speed1000                       0x08002a8b   Thumb Code    42  motor_driver.o(i.Float_To_Speed1000)
    i.Gray_Init                              0x08002ab4   Section        0  gray_app.o(i.Gray_Init)
    i.Gray_Task                              0x08002b20   Section        0  gray_app.o(i.Gray_Task)
    i.HAL_DMA_Abort                          0x08002b9c   Section        0  stm32f4xx_hal_dma.o(i.HAL_DMA_Abort)
    i.HAL_DMA_Abort_IT                       0x08002c48   Section        0  stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT)
    i.HAL_DMA_IRQHandler                     0x08002c70   Section        0  stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler)
    i.HAL_DMA_Init                           0x08002eb0   Section        0  stm32f4xx_hal_dma.o(i.HAL_DMA_Init)
    i.HAL_DMA_Start_IT                       0x08002f9c   Section        0  stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT)
    i.HAL_Delay                              0x08003030   Section        0  stm32f4xx_hal.o(i.HAL_Delay)
    i.HAL_GPIO_EXTI_Callback                 0x08003058   Section        0  stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback)
    i.HAL_GPIO_EXTI_IRQHandler               0x0800305c   Section        0  stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler)
    i.HAL_GPIO_Init                          0x08003078   Section        0  stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init)
    i.HAL_GPIO_ReadPin                       0x0800326c   Section        0  stm32f4xx_hal_gpio.o(i.HAL_GPIO_ReadPin)
    i.HAL_GPIO_WritePin                      0x0800327c   Section        0  stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin)
    i.HAL_GetTick                            0x08003288   Section        0  stm32f4xx_hal.o(i.HAL_GetTick)
    i.HAL_I2C_Init                           0x08003294   Section        0  stm32f4xx_hal_i2c.o(i.HAL_I2C_Init)
    i.HAL_I2C_Mem_Read                       0x08003464   Section        0  stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read)
    i.HAL_I2C_Mem_Write                      0x0800376c   Section        0  stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write)
    i.HAL_I2C_MspInit                        0x080038d0   Section        0  i2c.o(i.HAL_I2C_MspInit)
    i.HAL_IncTick                            0x080039c4   Section        0  stm32f4xx_hal.o(i.HAL_IncTick)
    i.HAL_Init                               0x080039dc   Section        0  stm32f4xx_hal.o(i.HAL_Init)
    i.HAL_InitTick                           0x08003a18   Section        0  stm32f4xx_hal.o(i.HAL_InitTick)
    i.HAL_MspInit                            0x08003a64   Section        0  stm32f4xx_hal_msp.o(i.HAL_MspInit)
    i.HAL_NVIC_EnableIRQ                     0x08003ab4   Section        0  stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ)
    i.HAL_NVIC_SetPriority                   0x08003adc   Section        0  stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority)
    i.HAL_NVIC_SetPriorityGrouping           0x08003b58   Section        0  stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping)
    i.HAL_RCC_ClockConfig                    0x08003b80   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig)
    i.HAL_RCC_GetHCLKFreq                    0x08003d04   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq)
    i.HAL_RCC_GetPCLK1Freq                   0x08003d10   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq)
    i.HAL_RCC_GetPCLK2Freq                   0x08003d30   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq)
    i.HAL_RCC_GetSysClockFreq                0x08003d50   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq)
    i.HAL_RCC_OscConfig                      0x08003e00   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig)
    i.HAL_SYSTICK_Config                     0x0800429c   Section        0  stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Config)
    i.HAL_TIMEx_BreakCallback                0x080042d0   Section        0  stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_BreakCallback)
    i.HAL_TIMEx_CommutCallback               0x080042d2   Section        0  stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback)
    i.HAL_TIMEx_ConfigBreakDeadTime          0x080042d4   Section        0  stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigBreakDeadTime)
    i.HAL_TIMEx_MasterConfigSynchronization  0x08004348   Section        0  stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization)
    i.HAL_TIM_Base_Init                      0x080043fc   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init)
    i.HAL_TIM_Base_MspInit                   0x08004464   Section        0  tim.o(i.HAL_TIM_Base_MspInit)
    i.HAL_TIM_Base_Start_IT                  0x080044d8   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_IT)
    i.HAL_TIM_ConfigClockSource              0x08004580   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource)
    i.HAL_TIM_Encoder_Init                   0x0800468c   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Init)
    i.HAL_TIM_Encoder_MspInit                0x08004754   Section        0  tim.o(i.HAL_TIM_Encoder_MspInit)
    i.HAL_TIM_Encoder_Start                  0x0800484c   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start)
    i.HAL_TIM_IC_CaptureCallback             0x08004918   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback)
    i.HAL_TIM_IRQHandler                     0x0800491a   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler)
    i.HAL_TIM_MspPostInit                    0x08004a88   Section        0  tim.o(i.HAL_TIM_MspPostInit)
    i.HAL_TIM_OC_DelayElapsedCallback        0x08004aec   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_OC_DelayElapsedCallback)
    i.HAL_TIM_PWM_ConfigChannel              0x08004aee   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel)
    i.HAL_TIM_PWM_Init                       0x08004bf2   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Init)
    i.HAL_TIM_PWM_MspInit                    0x08004c58   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_MspInit)
    i.HAL_TIM_PWM_PulseFinishedCallback      0x08004c5a   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback)
    i.HAL_TIM_PWM_Start                      0x08004c5c   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start)
    i.HAL_TIM_PeriodElapsedCallback          0x08004d68   Section        0  my_timer.o(i.HAL_TIM_PeriodElapsedCallback)
    i.HAL_TIM_TriggerCallback                0x08004eac   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerCallback)
    i.HAL_UARTEx_ReceiveToIdle_DMA           0x08004eae   Section        0  stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA)
    i.HAL_UARTEx_RxEventCallback             0x08004f20   Section        0  usart_app.o(i.HAL_UARTEx_RxEventCallback)
    i.HAL_UART_DMAStop                       0x08004f78   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop)
    i.HAL_UART_ErrorCallback                 0x08005002   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback)
    i.HAL_UART_IRQHandler                    0x08005004   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler)
    i.HAL_UART_Init                          0x0800530c   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_Init)
    i.HAL_UART_MspInit                       0x08005384   Section        0  usart.o(i.HAL_UART_MspInit)
    i.HAL_UART_RxCpltCallback                0x08005564   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_RxCpltCallback)
    i.HAL_UART_RxHalfCpltCallback            0x08005566   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback)
    i.HAL_UART_Transmit                      0x08005568   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_Transmit)
    i.HAL_UART_TxCpltCallback                0x08005626   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_TxCpltCallback)
    i.HardFault_Handler                      0x08005628   Section        0  stm32f4xx_it.o(i.HardFault_Handler)
    i.I2C_IsAcknowledgeFailed                0x0800562c   Section        0  stm32f4xx_hal_i2c.o(i.I2C_IsAcknowledgeFailed)
    I2C_IsAcknowledgeFailed                  0x0800562d   Thumb Code    62  stm32f4xx_hal_i2c.o(i.I2C_IsAcknowledgeFailed)
    i.I2C_RequestMemoryRead                  0x0800566c   Section        0  stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryRead)
    I2C_RequestMemoryRead                    0x0800566d   Thumb Code   348  stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryRead)
    i.I2C_RequestMemoryWrite                 0x080057cc   Section        0  stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryWrite)
    I2C_RequestMemoryWrite                   0x080057cd   Thumb Code   220  stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryWrite)
    i.I2C_WaitOnBTFFlagUntilTimeout          0x080058ac   Section        0  stm32f4xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout)
    I2C_WaitOnBTFFlagUntilTimeout            0x080058ad   Thumb Code   102  stm32f4xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout)
    i.I2C_WaitOnFlagUntilTimeout             0x08005912   Section        0  stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout)
    I2C_WaitOnFlagUntilTimeout               0x08005913   Thumb Code   190  stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout)
    i.I2C_WaitOnMasterAddressFlagUntilTimeout 0x080059d0   Section        0  stm32f4xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout)
    I2C_WaitOnMasterAddressFlagUntilTimeout  0x080059d1   Thumb Code   250  stm32f4xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout)
    i.I2C_WaitOnRXNEFlagUntilTimeout         0x08005aca   Section        0  stm32f4xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout)
    I2C_WaitOnRXNEFlagUntilTimeout           0x08005acb   Thumb Code   138  stm32f4xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout)
    i.I2C_WaitOnTXEFlagUntilTimeout          0x08005b54   Section        0  stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout)
    I2C_WaitOnTXEFlagUntilTimeout            0x08005b55   Thumb Code   102  stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout)
    i.IIC_Delay                              0x08005bba   Section        0  software_iic.o(i.IIC_Delay)
    IIC_Delay                                0x08005bbb   Thumb Code    10  software_iic.o(i.IIC_Delay)
    i.IIC_Get_Digtal                         0x08005bc4   Section        0  software_iic.o(i.IIC_Get_Digtal)
    i.IIC_ReadBytes                          0x08005be4   Section        0  software_iic.o(i.IIC_ReadBytes)
    i.IIC_RecvByte                           0x08005c5c   Section        0  software_iic.o(i.IIC_RecvByte)
    i.IIC_SendAck                            0x08005cd8   Section        0  software_iic.o(i.IIC_SendAck)
    i.IIC_SendByte                           0x08005d0c   Section        0  software_iic.o(i.IIC_SendByte)
    i.IIC_SendNAck                           0x08005d64   Section        0  software_iic.o(i.IIC_SendNAck)
    i.IIC_Start                              0x08005d90   Section        0  software_iic.o(i.IIC_Start)
    i.IIC_Stop                               0x08005dc8   Section        0  software_iic.o(i.IIC_Stop)
    i.IIC_WaitAck                            0x08005dfc   Section        0  software_iic.o(i.IIC_WaitAck)
    i.Line_PID_control                       0x08005e38   Section        0  pid_app.o(i.Line_PID_control)
    i.MPU6050_CalculateEuler                 0x08005ec0   Section        0  mpu6050_hal.o(i.MPU6050_CalculateEuler)
    i.MPU6050_CalibrateGyro                  0x08005ff0   Section        0  mpu6050_hal.o(i.MPU6050_CalibrateGyro)
    i.MPU6050_Config                         0x080060e0   Section        0  mpu6050_hal.o(i.MPU6050_Config)
    i.MPU6050_Init                           0x0800614c   Section        0  mpu6050_hal.o(i.MPU6050_Init)
    i.MPU6050_ReadData                       0x080061d4   Section        0  mpu6050_hal.o(i.MPU6050_ReadData)
    i.MPU6050_ReadRawData                    0x080062ec   Section        0  mpu6050_hal.o(i.MPU6050_ReadRawData)
    i.MPU6050_ReadReg                        0x08006380   Section        0  mpu6050_hal.o(i.MPU6050_ReadReg)
    MPU6050_ReadReg                          0x08006381   Thumb Code    48  mpu6050_hal.o(i.MPU6050_ReadReg)
    i.MPU6050_ReadRegs                       0x080063b8   Section        0  mpu6050_hal.o(i.MPU6050_ReadRegs)
    MPU6050_ReadRegs                         0x080063b9   Thumb Code    48  mpu6050_hal.o(i.MPU6050_ReadRegs)
    i.MPU6050_Reset                          0x080063f0   Section        0  mpu6050_hal.o(i.MPU6050_Reset)
    i.MPU6050_TestConnection                 0x080063fc   Section        0  mpu6050_hal.o(i.MPU6050_TestConnection)
    i.MPU6050_UpdateScales                   0x08006420   Section        0  mpu6050_hal.o(i.MPU6050_UpdateScales)
    MPU6050_UpdateScales                     0x08006421   Thumb Code   166  mpu6050_hal.o(i.MPU6050_UpdateScales)
    i.MPU6050_WriteReg                       0x080064f4   Section        0  mpu6050_hal.o(i.MPU6050_WriteReg)
    MPU6050_WriteReg                         0x080064f5   Thumb Code    52  mpu6050_hal.o(i.MPU6050_WriteReg)
    i.MX_DMA_Init                            0x08006530   Section        0  dma.o(i.MX_DMA_Init)
    i.MX_GPIO_Init                           0x08006598   Section        0  gpio.o(i.MX_GPIO_Init)
    i.MX_I2C1_Init                           0x08006764   Section        0  i2c.o(i.MX_I2C1_Init)
    i.MX_I2C2_Init                           0x080067a0   Section        0  i2c.o(i.MX_I2C2_Init)
    i.MX_TIM1_Init                           0x080067dc   Section        0  tim.o(i.MX_TIM1_Init)
    i.MX_TIM2_Init                           0x080068e4   Section        0  tim.o(i.MX_TIM2_Init)
    i.MX_TIM3_Init                           0x08006950   Section        0  tim.o(i.MX_TIM3_Init)
    i.MX_TIM4_Init                           0x080069d0   Section        0  tim.o(i.MX_TIM4_Init)
    i.MX_UART5_Init                          0x08006a50   Section        0  usart.o(i.MX_UART5_Init)
    i.MX_USART1_UART_Init                    0x08006a88   Section        0  usart.o(i.MX_USART1_UART_Init)
    i.MemManage_Handler                      0x08006ac0   Section        0  stm32f4xx_it.o(i.MemManage_Handler)
    i.Motor_Create                           0x08006ac4   Section        0  motor_driver.o(i.Motor_Create)
    i.Motor_Init                             0x08006b88   Section        0  motor_app.o(i.Motor_Init)
    i.Motor_SetSpeed                         0x08006be8   Section        0  motor_driver.o(i.Motor_SetSpeed)
    i.Motor_ValidateFloatSpeed               0x08006c88   Section        0  motor_driver.o(i.Motor_ValidateFloatSpeed)
    Motor_ValidateFloatSpeed                 0x08006c89   Thumb Code    38  motor_driver.o(i.Motor_ValidateFloatSpeed)
    i.Motor_ValidateParams                   0x08006cb8   Section        0  motor_driver.o(i.Motor_ValidateParams)
    Motor_ValidateParams                     0x08006cb9   Thumb Code    26  motor_driver.o(i.Motor_ValidateParams)
    i.NMI_Handler                            0x08006cd2   Section        0  stm32f4xx_it.o(i.NMI_Handler)
    i.OLED_Clear                             0x08006cd6   Section        0  oled.o(i.OLED_Clear)
    i.OLED_Init                              0x08006d10   Section        0  oled.o(i.OLED_Init)
    i.OLED_Set_Position                      0x08006d40   Section        0  oled.o(i.OLED_Set_Position)
    i.OLED_ShowChar                          0x08006d64   Section        0  oled.o(i.OLED_ShowChar)
    i.OLED_ShowStr                           0x08006e00   Section        0  oled.o(i.OLED_ShowStr)
    i.OLED_Write_cmd                         0x08006e3c   Section        0  oled.o(i.OLED_Write_cmd)
    i.OLED_Write_data                        0x08006e60   Section        0  oled.o(i.OLED_Write_data)
    i.PID_Init                               0x08006e84   Section        0  pid_app.o(i.PID_Init)
    i.PID_Task                               0x08006f94   Section        0  pid_app.o(i.PID_Task)
    i.PendSV_Handler                         0x080070e8   Section        0  stm32f4xx_it.o(i.PendSV_Handler)
    i.Ping                                   0x080070ea   Section        0  software_iic.o(i.Ping)
    i.SVC_Handler                            0x08007108   Section        0  stm32f4xx_it.o(i.SVC_Handler)
    i.Set_Pin_Mode                           0x0800710a   Section        0  motor_driver.o(i.Set_Pin_Mode)
    Set_Pin_Mode                             0x0800710b   Thumb Code   102  motor_driver.o(i.Set_Pin_Mode)
    i.Speed1000_To_PWM                       0x08007170   Section        0  motor_driver.o(i.Speed1000_To_PWM)
    Speed1000_To_PWM                         0x08007171   Thumb Code    88  motor_driver.o(i.Speed1000_To_PWM)
    i.SysTick_Handler                        0x080071c8   Section        0  stm32f4xx_it.o(i.SysTick_Handler)
    i.SystemClock_Config                     0x080071d0   Section        0  main.o(i.SystemClock_Config)
    i.SystemInit                             0x08007284   Section        0  system_stm32f4xx.o(i.SystemInit)
    i.TIM2_IRQHandler                        0x08007298   Section        0  stm32f4xx_it.o(i.TIM2_IRQHandler)
    i.TIM_Base_SetConfig                     0x080072a8   Section        0  stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig)
    i.TIM_CCxChannelCmd                      0x08007388   Section        0  stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd)
    i.TIM_ETR_SetConfig                      0x080073aa   Section        0  stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig)
    i.TIM_ITRx_SetConfig                     0x080073c0   Section        0  stm32f4xx_hal_tim.o(i.TIM_ITRx_SetConfig)
    TIM_ITRx_SetConfig                       0x080073c1   Thumb Code    18  stm32f4xx_hal_tim.o(i.TIM_ITRx_SetConfig)
    i.TIM_OC1_SetConfig                      0x080073d4   Section        0  stm32f4xx_hal_tim.o(i.TIM_OC1_SetConfig)
    TIM_OC1_SetConfig                        0x080073d5   Thumb Code   104  stm32f4xx_hal_tim.o(i.TIM_OC1_SetConfig)
    i.TIM_OC2_SetConfig                      0x08007444   Section        0  stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig)
    i.TIM_OC3_SetConfig                      0x080074c0   Section        0  stm32f4xx_hal_tim.o(i.TIM_OC3_SetConfig)
    TIM_OC3_SetConfig                        0x080074c1   Thumb Code   112  stm32f4xx_hal_tim.o(i.TIM_OC3_SetConfig)
    i.TIM_OC4_SetConfig                      0x08007538   Section        0  stm32f4xx_hal_tim.o(i.TIM_OC4_SetConfig)
    TIM_OC4_SetConfig                        0x08007539   Thumb Code    74  stm32f4xx_hal_tim.o(i.TIM_OC4_SetConfig)
    i.TIM_TI1_ConfigInputStage               0x0800758c   Section        0  stm32f4xx_hal_tim.o(i.TIM_TI1_ConfigInputStage)
    TIM_TI1_ConfigInputStage                 0x0800758d   Thumb Code    38  stm32f4xx_hal_tim.o(i.TIM_TI1_ConfigInputStage)
    i.TIM_TI2_ConfigInputStage               0x080075b2   Section        0  stm32f4xx_hal_tim.o(i.TIM_TI2_ConfigInputStage)
    TIM_TI2_ConfigInputStage                 0x080075b3   Thumb Code    40  stm32f4xx_hal_tim.o(i.TIM_TI2_ConfigInputStage)
    i.UART5_IRQHandler                       0x080075dc   Section        0  stm32f4xx_it.o(i.UART5_IRQHandler)
    i.UART_DMAAbortOnError                   0x080075ec   Section        0  stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError)
    UART_DMAAbortOnError                     0x080075ed   Thumb Code    18  stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError)
    i.UART_DMAError                          0x080075fe   Section        0  stm32f4xx_hal_uart.o(i.UART_DMAError)
    UART_DMAError                            0x080075ff   Thumb Code    80  stm32f4xx_hal_uart.o(i.UART_DMAError)
    i.UART_DMAReceiveCplt                    0x0800764e   Section        0  stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt)
    UART_DMAReceiveCplt                      0x0800764f   Thumb Code   180  stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt)
    i.UART_DMARxHalfCplt                     0x08007702   Section        0  stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt)
    UART_DMARxHalfCplt                       0x08007703   Thumb Code    36  stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt)
    i.UART_EndRxTransfer                     0x08007726   Section        0  stm32f4xx_hal_uart.o(i.UART_EndRxTransfer)
    UART_EndRxTransfer                       0x08007727   Thumb Code   108  stm32f4xx_hal_uart.o(i.UART_EndRxTransfer)
    i.UART_EndTransmit_IT                    0x08007792   Section        0  stm32f4xx_hal_uart.o(i.UART_EndTransmit_IT)
    UART_EndTransmit_IT                      0x08007793   Thumb Code    32  stm32f4xx_hal_uart.o(i.UART_EndTransmit_IT)
    i.UART_EndTxTransfer                     0x080077b2   Section        0  stm32f4xx_hal_uart.o(i.UART_EndTxTransfer)
    UART_EndTxTransfer                       0x080077b3   Thumb Code    38  stm32f4xx_hal_uart.o(i.UART_EndTxTransfer)
    i.UART_Receive_IT                        0x080077d8   Section        0  stm32f4xx_hal_uart.o(i.UART_Receive_IT)
    UART_Receive_IT                          0x080077d9   Thumb Code   252  stm32f4xx_hal_uart.o(i.UART_Receive_IT)
    i.UART_SetConfig                         0x080078d4   Section        0  stm32f4xx_hal_uart.o(i.UART_SetConfig)
    UART_SetConfig                           0x080078d5   Thumb Code   546  stm32f4xx_hal_uart.o(i.UART_SetConfig)
    i.UART_Start_Receive_DMA                 0x08007b00   Section        0  stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA)
    i.UART_Transmit_IT                       0x08007bd8   Section        0  stm32f4xx_hal_uart.o(i.UART_Transmit_IT)
    UART_Transmit_IT                         0x08007bd9   Thumb Code    96  stm32f4xx_hal_uart.o(i.UART_Transmit_IT)
    i.UART_WaitOnFlagUntilTimeout            0x08007c38   Section        0  stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout)
    UART_WaitOnFlagUntilTimeout              0x08007c39   Thumb Code   140  stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout)
    i.USART1_IRQHandler                      0x08007cc4   Section        0  stm32f4xx_it.o(i.USART1_IRQHandler)
    i.UsageFault_Handler                     0x08007cd4   Section        0  stm32f4xx_it.o(i.UsageFault_Handler)
    i.__ARM_fpclassify                       0x08007cd8   Section        0  fpclassify.o(i.__ARM_fpclassify)
    i.__ARM_fpclassifyf                      0x08007d08   Section        0  fpclassifyf.o(i.__ARM_fpclassifyf)
    i.__NVIC_GetPriorityGrouping             0x08007d30   Section        0  stm32f4xx_hal_cortex.o(i.__NVIC_GetPriorityGrouping)
    __NVIC_GetPriorityGrouping               0x08007d31   Thumb Code    10  stm32f4xx_hal_cortex.o(i.__NVIC_GetPriorityGrouping)
    i.__NVIC_SetPriority                     0x08007d40   Section        0  stm32f4xx_hal_cortex.o(i.__NVIC_SetPriority)
    __NVIC_SetPriority                       0x08007d41   Thumb Code    32  stm32f4xx_hal_cortex.o(i.__NVIC_SetPriority)
    i.__hardfp_atan2f                        0x08007d68   Section        0  atan2f.o(i.__hardfp_atan2f)
    i.__hardfp_roundf                        0x08008014   Section        0  roundf.o(i.__hardfp_roundf)
    i.__hardfp_sqrtf                         0x080080ae   Section        0  sqrtf.o(i.__hardfp_sqrtf)
    i.__mathlib_flt_infnan2                  0x080080e8   Section        0  funder.o(i.__mathlib_flt_infnan2)
    i.__mathlib_flt_underflow                0x080080f0   Section        0  funder.o(i.__mathlib_flt_underflow)
    i._is_digit                              0x08008100   Section        0  __printf_wp.o(i._is_digit)
    i.all_task_init                          0x08008110   Section        0  my_scheduler.o(i.all_task_init)
    i.all_task_run                           0x08008144   Section        0  my_scheduler.o(i.all_task_run)
    i.bno080_task                            0x0800819c   Section        0  mpu6050_app.o(i.bno080_task)
    i.convert_to_continuous_yaw              0x0800823c   Section        0  mpu6050_app.o(i.convert_to_continuous_yaw)
    i.get_yaw                                0x080082d4   Section        0  mpu6050_app.o(i.get_yaw)
    i.key_read                               0x080082f0   Section        0  key_driver.o(i.key_read)
    i.key_task                               0x0800833c   Section        0  key_app.o(i.key_task)
    i.led_disp                               0x080083c8   Section        0  led_driver.o(i.led_disp)
    i.led_init                               0x0800844c   Section        0  led_app.o(i.led_init)
    i.led_task                               0x08008450   Section        0  led_app.o(i.led_task)
    i.main                                   0x08008460   Section        0  main.o(i.main)
    i.motor_set_l                            0x0800849c   Section        0  motor_app.o(i.motor_set_l)
    i.motor_set_r                            0x080084bc   Section        0  motor_app.o(i.motor_set_r)
    i.my_bno080_init                         0x080084dc   Section        0  mpu6050_app.o(i.my_bno080_init)
    i.my_oled_init                           0x080086cc   Section        0  oled_app.o(i.my_oled_init)
    i.my_printf                              0x080086d4   Section        0  usart_app.o(i.my_printf)
    i.oled_printf                            0x0800870e   Section        0  oled_app.o(i.oled_printf)
    i.oled_task                              0x0800874c   Section        0  oled_app.o(i.oled_task)
    i.pid_calculate_positional               0x08008810   Section        0  pid.o(i.pid_calculate_positional)
    i.pid_constrain                          0x08008836   Section        0  pid.o(i.pid_constrain)
    i.pid_formula_positional                 0x08008860   Section        0  pid.o(i.pid_formula_positional)
    pid_formula_positional                   0x08008861   Thumb Code   122  pid.o(i.pid_formula_positional)
    i.pid_init                               0x080088dc   Section        0  pid.o(i.pid_init)
    i.pid_out_limit                          0x08008930   Section        0  pid.o(i.pid_out_limit)
    pid_out_limit                            0x08008931   Thumb Code    64  pid.o(i.pid_out_limit)
    i.pid_reset                              0x08008970   Section        0  pid.o(i.pid_reset)
    i.pid_set_target                         0x080089b0   Section        0  pid.o(i.pid_set_target)
    i.rt_ringbuffer_data_len                 0x080089b6   Section        0  ringbuffer.o(i.rt_ringbuffer_data_len)
    i.rt_ringbuffer_get                      0x080089f4   Section        0  ringbuffer.o(i.rt_ringbuffer_get)
    i.rt_ringbuffer_init                     0x08008ae0   Section        0  ringbuffer.o(i.rt_ringbuffer_init)
    i.rt_ringbuffer_put                      0x08008b74   Section        0  ringbuffer.o(i.rt_ringbuffer_put)
    i.rt_ringbuffer_status                   0x08008c64   Section        0  ringbuffer.o(i.rt_ringbuffer_status)
    rt_ringbuffer_status                     0x08008c65   Thumb Code    42  ringbuffer.o(i.rt_ringbuffer_status)
    i.timer_init                             0x08008c90   Section        0  my_timer.o(i.timer_init)
    i.uart_init                              0x08008ca0   Section        0  usart_app.o(i.uart_init)
    i.uart_task                              0x08008cdc   Section        0  usart_app.o(i.uart_task)
    locale$$code                             0x08008dd4   Section       44  lc_numeric_c.o(locale$$code)
    locale$$code                             0x08008e00   Section       44  lc_ctype_c.o(locale$$code)
    x$fpl$d2f                                0x08008e2c   Section       98  d2f.o(x$fpl$d2f)
    $v0                                      0x08008e2c   Number         0  d2f.o(x$fpl$d2f)
    x$fpl$ddiv                               0x08008e90   Section      688  ddiv.o(x$fpl$ddiv)
    $v0                                      0x08008e90   Number         0  ddiv.o(x$fpl$ddiv)
    ddiv_entry                               0x08008e97   Thumb Code     0  ddiv.o(x$fpl$ddiv)
    x$fpl$dnaninf                            0x08009140   Section      156  dnaninf.o(x$fpl$dnaninf)
    $v0                                      0x08009140   Number         0  dnaninf.o(x$fpl$dnaninf)
    x$fpl$dretinf                            0x080091dc   Section       12  dretinf.o(x$fpl$dretinf)
    $v0                                      0x080091dc   Number         0  dretinf.o(x$fpl$dretinf)
    x$fpl$f2d                                0x080091e8   Section       86  f2d.o(x$fpl$f2d)
    $v0                                      0x080091e8   Number         0  f2d.o(x$fpl$f2d)
    x$fpl$fnaninf                            0x0800923e   Section      140  fnaninf.o(x$fpl$fnaninf)
    $v0                                      0x0800923e   Number         0  fnaninf.o(x$fpl$fnaninf)
    x$fpl$fpinit                             0x080092ca   Section       10  fpinit.o(x$fpl$fpinit)
    $v0                                      0x080092ca   Number         0  fpinit.o(x$fpl$fpinit)
    x$fpl$fretinf                            0x080092d4   Section       10  fretinf.o(x$fpl$fretinf)
    $v0                                      0x080092d4   Number         0  fretinf.o(x$fpl$fretinf)
    x$fpl$frnd                               0x080092e0   Section       96  frnd.o(x$fpl$frnd)
    $v0                                      0x080092e0   Number         0  frnd.o(x$fpl$frnd)
    x$fpl$printf1                            0x08009340   Section        4  printf1.o(x$fpl$printf1)
    $v0                                      0x08009340   Number         0  printf1.o(x$fpl$printf1)
    x$fpl$printf2                            0x08009344   Section        4  printf2.o(x$fpl$printf2)
    $v0                                      0x08009344   Number         0  printf2.o(x$fpl$printf2)
    .constdata                               0x08009348   Section        8  stm32f4xx_hal_dma.o(.constdata)
    x$fpl$usenofp                            0x08009348   Section        0  usenofp.o(x$fpl$usenofp)
    flagBitshiftOffset                       0x08009348   Data           8  stm32f4xx_hal_dma.o(.constdata)
    .constdata                               0x08009350   Section       24  system_stm32f4xx.o(.constdata)
    .constdata                               0x08009368   Section     2712  oled.o(.constdata)
    F8X16                                    0x08009590   Data        1520  oled.o(.constdata)
    .constdata                               0x08009e00   Section        4  mpu6050_app.o(.constdata)
    .constdata                               0x08009e04   Section        8  _printf_wctomb.o(.constdata)
    initial_mbstate                          0x08009e04   Data           8  _printf_wctomb.o(.constdata)
    .constdata                               0x08009e0c   Section       40  _printf_hex_int_ll_ptr.o(.constdata)
    uc_hextab                                0x08009e0c   Data          20  _printf_hex_int_ll_ptr.o(.constdata)
    lc_hextab                                0x08009e20   Data          20  _printf_hex_int_ll_ptr.o(.constdata)
    .constdata                               0x08009e34   Section       17  __printf_flags_ss_wp.o(.constdata)
    maptable                                 0x08009e34   Data          17  __printf_flags_ss_wp.o(.constdata)
    .constdata                               0x08009e45   Section       38  _printf_fp_hex.o(.constdata)
    lc_hextab                                0x08009e45   Data          19  _printf_fp_hex.o(.constdata)
    uc_hextab                                0x08009e58   Data          19  _printf_fp_hex.o(.constdata)
    .constdata                               0x08009e6c   Section      148  bigflt0.o(.constdata)
    tenpwrs_x                                0x08009e6c   Data          60  bigflt0.o(.constdata)
    tenpwrs_i                                0x08009ea8   Data          64  bigflt0.o(.constdata)
    locale$$data                             0x08009f20   Section       28  lc_numeric_c.o(locale$$data)
    __lcnum_c_name                           0x08009f24   Data           2  lc_numeric_c.o(locale$$data)
    __lcnum_c_start                          0x08009f2c   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_point                          0x08009f38   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_thousands                      0x08009f3a   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_grouping                       0x08009f3b   Data           0  lc_numeric_c.o(locale$$data)
    locale$$data                             0x08009f3c   Section      272  lc_ctype_c.o(locale$$data)
    __lcnum_c_end                            0x08009f3c   Data           0  lc_numeric_c.o(locale$$data)
    __lcctype_c_name                         0x08009f40   Data           2  lc_ctype_c.o(locale$$data)
    __lcctype_c_start                        0x08009f48   Data           0  lc_ctype_c.o(locale$$data)
    __lcctype_c_end                          0x0800a04c   Data           0  lc_ctype_c.o(locale$$data)
    .data                                    0x20000000   Section        9  stm32f4xx_hal.o(.data)
    .data                                    0x2000000c   Section        4  system_stm32f4xx.o(.data)
    .data                                    0x20000010   Section       22  oled.o(.data)
    .data                                    0x20000028   Section       44  mpu6050_hal.o(.data)
    mpu6050_i2c                              0x20000028   Data           4  mpu6050_hal.o(.data)
    mpu6050_addr                             0x2000002c   Data           1  mpu6050_hal.o(.data)
    mpu6050_config                           0x2000002d   Data           4  mpu6050_hal.o(.data)
    accel_scale                              0x20000034   Data           4  mpu6050_hal.o(.data)
    gyro_scale                               0x20000038   Data           4  mpu6050_hal.o(.data)
    gyro_offset_x                            0x2000003c   Data           4  mpu6050_hal.o(.data)
    gyro_offset_y                            0x20000040   Data           4  mpu6050_hal.o(.data)
    gyro_offset_z                            0x20000044   Data           4  mpu6050_hal.o(.data)
    dt                                       0x20000048   Data           4  mpu6050_hal.o(.data)
    first_run                                0x2000004c   Data           1  mpu6050_hal.o(.data)
    alpha                                    0x20000050   Data           4  mpu6050_hal.o(.data)
    .data                                    0x20000054   Section        1  led_driver.o(.data)
    temp_old                                 0x20000054   Data           1  led_driver.o(.data)
    .data                                    0x20000055   Section        5  led_app.o(.data)
    .data                                    0x2000005a   Section        6  key_app.o(.data)
    key_old                                  0x2000005f   Data           1  key_app.o(.data)
    .data                                    0x20000060   Section       88  pid_app.o(.data)
    .data                                    0x200000b8   Section       40  gray_app.o(.data)
    .data                                    0x200000e0   Section       37  mpu6050_app.o(.data)
    data_ready                               0x200000e0   Data           1  mpu6050_app.o(.data)
    last_update_time                         0x200000e4   Data           4  mpu6050_app.o(.data)
    .data                                    0x20000108   Section       61  my_scheduler.o(.data)
    .data                                    0x20000148   Section       28  my_timer.o(.data)
    .bss                                     0x20000164   Section      168  i2c.o(.bss)
    .bss                                     0x2000020c   Section      288  tim.o(.bss)
    .bss                                     0x2000032c   Section      336  usart.o(.bss)
    .bss                                     0x2000047c   Section      396  usart_app.o(.bss)
    .bss                                     0x20000608   Section       96  motor_app.o(.bss)
    .bss                                     0x20000668   Section       32  encoder_app.o(.bss)
    .bss                                     0x20000688   Section      240  pid_app.o(.bss)
    .bss                                     0x20000778   Section       40  mpu6050_app.o(.bss)
    mpu6050_data                             0x20000778   Data          28  mpu6050_app.o(.bss)
    mpu6050_euler                            0x20000794   Data          12  mpu6050_app.o(.bss)
    .bss                                     0x200007a0   Section       96  libspace.o(.bss)
    HEAP                                     0x20000800   Section     4096  startup_stm32f407xx.o(HEAP)
    Heap_Mem                                 0x20000800   Data        4096  startup_stm32f407xx.o(HEAP)
    STACK                                    0x20001800   Section     8192  startup_stm32f407xx.o(STACK)
    Stack_Mem                                0x20001800   Data        8192  startup_stm32f407xx.o(STACK)
    __initial_sp                             0x20003800   Data           0  startup_stm32f407xx.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$E$P$D$K$B$S$7EM$VFPi3$EXTD16$VFPS$VFMA$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEX$EBA8$UX$STANDARDLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    _printf_flags                            0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_return_value                     0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_sizespec                         0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_widthprec                        0x00000000   Number         0  printf_stubs.o ABSOLUTE
    __ARM_exceptions_init                     - Undefined Weak Reference
    __alloca_initialize                       - Undefined Weak Reference
    __arm_fini_                               - Undefined Weak Reference
    __arm_preinit_                            - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __sigvec_lookup                           - Undefined Weak Reference
    _atexit_init                              - Undefined Weak Reference
    _call_atexit_fns                          - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _fp_trap_init                             - Undefined Weak Reference
    _fp_trap_shutdown                         - Undefined Weak Reference
    _get_lc_collate                           - Undefined Weak Reference
    _get_lc_monetary                          - Undefined Weak Reference
    _get_lc_time                              - Undefined Weak Reference
    _getenv_init                              - Undefined Weak Reference
    _handle_redirection                       - Undefined Weak Reference
    _init_alloc                               - Undefined Weak Reference
    _init_user_alloc                          - Undefined Weak Reference
    _initio                                   - Undefined Weak Reference
    _printf_mbtowc                            - Undefined Weak Reference
    _printf_wc                                - Undefined Weak Reference
    _rand_init                                - Undefined Weak Reference
    _scanf_longlong                           - Undefined Weak Reference
    _scanf_mbtowc                             - Undefined Weak Reference
    _scanf_real                               - Undefined Weak Reference
    _scanf_string                             - Undefined Weak Reference
    _scanf_wctomb                             - Undefined Weak Reference
    _scanf_wstring                            - Undefined Weak Reference
    _signal_finish                            - Undefined Weak Reference
    _signal_init                              - Undefined Weak Reference
    _terminate_alloc                          - Undefined Weak Reference
    _terminate_user_alloc                     - Undefined Weak Reference
    _terminateio                              - Undefined Weak Reference
    __Vectors_Size                           0x00000188   Number         0  startup_stm32f407xx.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f407xx.o(RESET)
    __Vectors_End                            0x08000188   Data           0  startup_stm32f407xx.o(RESET)
    __main                                   0x08000189   Thumb Code     8  __main.o(!!!main)
    __scatterload                            0x08000191   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_rt2                        0x08000191   Thumb Code    44  __scatter.o(!!!scatter)
    __scatterload_rt2_thumb_only             0x08000191   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_null                       0x0800019f   Thumb Code     0  __scatter.o(!!!scatter)
    __decompress                             0x080001c5   Thumb Code    90  __dczerorl2.o(!!dczerorl2)
    __decompress1                            0x080001c5   Thumb Code     0  __dczerorl2.o(!!dczerorl2)
    __scatterload_zeroinit                   0x08000221   Thumb Code    28  __scatter_zi.o(!!handler_zi)
    _printf_n                                0x0800023d   Thumb Code     0  _printf_n.o(.ARM.Collect$$_printf_percent$$00000001)
    _printf_percent                          0x0800023d   Thumb Code     0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    _printf_p                                0x08000243   Thumb Code     0  _printf_p.o(.ARM.Collect$$_printf_percent$$00000002)
    _printf_f                                0x08000249   Thumb Code     0  _printf_f.o(.ARM.Collect$$_printf_percent$$00000003)
    _printf_e                                0x0800024f   Thumb Code     0  _printf_e.o(.ARM.Collect$$_printf_percent$$00000004)
    _printf_g                                0x08000255   Thumb Code     0  _printf_g.o(.ARM.Collect$$_printf_percent$$00000005)
    _printf_a                                0x0800025b   Thumb Code     0  _printf_a.o(.ARM.Collect$$_printf_percent$$00000006)
    _printf_ll                               0x08000261   Thumb Code     0  _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007)
    _printf_i                                0x0800026b   Thumb Code     0  _printf_i.o(.ARM.Collect$$_printf_percent$$00000008)
    _printf_d                                0x08000271   Thumb Code     0  _printf_d.o(.ARM.Collect$$_printf_percent$$00000009)
    _printf_u                                0x08000277   Thumb Code     0  _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A)
    _printf_o                                0x0800027d   Thumb Code     0  _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B)
    _printf_x                                0x08000283   Thumb Code     0  _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C)
    _printf_lli                              0x08000289   Thumb Code     0  _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D)
    _printf_lld                              0x0800028f   Thumb Code     0  _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E)
    _printf_llu                              0x08000295   Thumb Code     0  _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F)
    _printf_llo                              0x0800029b   Thumb Code     0  _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010)
    _printf_llx                              0x080002a1   Thumb Code     0  _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011)
    _printf_l                                0x080002a7   Thumb Code     0  _printf_l.o(.ARM.Collect$$_printf_percent$$00000012)
    _printf_c                                0x080002b1   Thumb Code     0  _printf_c.o(.ARM.Collect$$_printf_percent$$00000013)
    _printf_s                                0x080002b7   Thumb Code     0  _printf_s.o(.ARM.Collect$$_printf_percent$$00000014)
    _printf_lc                               0x080002bd   Thumb Code     0  _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015)
    _printf_ls                               0x080002c3   Thumb Code     0  _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016)
    _printf_percent_end                      0x080002c9   Thumb Code     0  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    __rt_lib_init                            0x080002cd   Thumb Code     0  libinit.o(.ARM.Collect$$libinit$$00000000)
    __rt_lib_init_fp_1                       0x080002cf   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000001)
    __rt_lib_init_heap_1                     0x080002d3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    __rt_lib_init_lc_common                  0x080002d3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000F)
    __rt_lib_init_preinit_1                  0x080002d3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    __rt_lib_init_rand_1                     0x080002d3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    __rt_lib_init_user_alloc_1               0x080002d3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    __rt_lib_init_lc_collate_1               0x080002d9   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    __rt_lib_init_lc_ctype_2                 0x080002d9   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000012)
    __rt_lib_init_lc_ctype_1                 0x080002e5   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    __rt_lib_init_lc_monetary_1              0x080002e5   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    __rt_lib_init_lc_numeric_2               0x080002e5   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000016)
    __rt_lib_init_alloca_1                   0x080002ef   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    __rt_lib_init_argv_1                     0x080002ef   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    __rt_lib_init_atexit_1                   0x080002ef   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    __rt_lib_init_clock_1                    0x080002ef   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    __rt_lib_init_cpp_1                      0x080002ef   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    __rt_lib_init_exceptions_1               0x080002ef   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    __rt_lib_init_fp_trap_1                  0x080002ef   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    __rt_lib_init_getenv_1                   0x080002ef   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    __rt_lib_init_lc_numeric_1               0x080002ef   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    __rt_lib_init_lc_time_1                  0x080002ef   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    __rt_lib_init_return                     0x080002ef   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000033)
    __rt_lib_init_signal_1                   0x080002ef   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    __rt_lib_init_stdio_1                    0x080002ef   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    __rt_lib_shutdown                        0x080002f1   Thumb Code     0  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    __rt_lib_shutdown_cpp_1                  0x080002f3   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    __rt_lib_shutdown_fini_1                 0x080002f3   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    __rt_lib_shutdown_fp_trap_1              0x080002f3   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000009)
    __rt_lib_shutdown_heap_1                 0x080002f3   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000011)
    __rt_lib_shutdown_return                 0x080002f3   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000012)
    __rt_lib_shutdown_signal_1               0x080002f3   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    __rt_lib_shutdown_stdio_1                0x080002f3   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000006)
    __rt_lib_shutdown_user_alloc_1           0x080002f3   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E)
    __rt_entry                               0x080002f5   Thumb Code     0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    __rt_entry_presh_1                       0x080002f5   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    __rt_entry_sh                            0x080002f5   Thumb Code     0  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    __rt_entry_li                            0x080002fb   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    __rt_entry_postsh_1                      0x080002fb   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    __rt_entry_main                          0x080002ff   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    __rt_entry_postli_1                      0x080002ff   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    __rt_exit                                0x08000307   Thumb Code     0  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    __rt_exit_ls                             0x08000309   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    __rt_exit_prels_1                        0x08000309   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    __rt_exit_exit                           0x0800030d   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    Reset_Handler                            0x08000315   Thumb Code     8  startup_stm32f407xx.o(.text)
    ADC_IRQHandler                           0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_RX0_IRQHandler                      0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_RX1_IRQHandler                      0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_SCE_IRQHandler                      0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_TX_IRQHandler                       0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_RX0_IRQHandler                      0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_RX1_IRQHandler                      0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_SCE_IRQHandler                      0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_TX_IRQHandler                       0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    DCMI_IRQHandler                          0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream1_IRQHandler                  0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream2_IRQHandler                  0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream3_IRQHandler                  0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream4_IRQHandler                  0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream5_IRQHandler                  0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream6_IRQHandler                  0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream7_IRQHandler                  0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream0_IRQHandler                  0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream1_IRQHandler                  0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream3_IRQHandler                  0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream4_IRQHandler                  0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream5_IRQHandler                  0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream6_IRQHandler                  0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream7_IRQHandler                  0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    ETH_IRQHandler                           0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    ETH_WKUP_IRQHandler                      0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI0_IRQHandler                         0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI15_10_IRQHandler                     0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI1_IRQHandler                         0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI2_IRQHandler                         0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI4_IRQHandler                         0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI9_5_IRQHandler                       0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    FLASH_IRQHandler                         0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    FMC_IRQHandler                           0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    FPU_IRQHandler                           0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    HASH_RNG_IRQHandler                      0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C1_ER_IRQHandler                       0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C1_EV_IRQHandler                       0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C2_ER_IRQHandler                       0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C2_EV_IRQHandler                       0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C3_ER_IRQHandler                       0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C3_EV_IRQHandler                       0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_FS_IRQHandler                        0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_FS_WKUP_IRQHandler                   0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_EP1_IN_IRQHandler                 0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_EP1_OUT_IRQHandler                0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_IRQHandler                        0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_WKUP_IRQHandler                   0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    PVD_IRQHandler                           0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    RCC_IRQHandler                           0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    RTC_Alarm_IRQHandler                     0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    RTC_WKUP_IRQHandler                      0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    SDIO_IRQHandler                          0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    SPI1_IRQHandler                          0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    SPI2_IRQHandler                          0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    SPI3_IRQHandler                          0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    TAMP_STAMP_IRQHandler                    0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_BRK_TIM9_IRQHandler                 0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_CC_IRQHandler                       0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_TRG_COM_TIM11_IRQHandler            0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_UP_TIM10_IRQHandler                 0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM3_IRQHandler                          0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM4_IRQHandler                          0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM5_IRQHandler                          0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM6_DAC_IRQHandler                      0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM7_IRQHandler                          0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_BRK_TIM12_IRQHandler                0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_CC_IRQHandler                       0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_TRG_COM_TIM14_IRQHandler            0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_UP_TIM13_IRQHandler                 0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    UART4_IRQHandler                         0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    USART2_IRQHandler                        0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    USART3_IRQHandler                        0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    USART6_IRQHandler                        0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    WWDG_IRQHandler                          0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    __user_initial_stackheap                 0x08000331   Thumb Code     0  startup_stm32f407xx.o(.text)
    __aeabi_uldivmod                         0x08000355   Thumb Code     0  lludivv7m.o(.text)
    _ll_udiv                                 0x08000355   Thumb Code   238  lludivv7m.o(.text)
    vsnprintf                                0x08000445   Thumb Code    48  vsnprintf.o(.text)
    __0sscanf                                0x08000479   Thumb Code    52  __0sscanf.o(.text)
    _scanf_int                               0x080004b5   Thumb Code   332  _scanf_int.o(.text)
    __aeabi_assert                           0x08000601   Thumb Code    86  assert.o(.text)
    __assert                                 0x08000601   Thumb Code     0  assert.o(.text)
    __aeabi_memcpy                           0x08000681   Thumb Code     0  rt_memcpy_v6.o(.text)
    __rt_memcpy                              0x08000681   Thumb Code   138  rt_memcpy_v6.o(.text)
    _memcpy_lastbytes                        0x080006e7   Thumb Code     0  rt_memcpy_v6.o(.text)
    __aeabi_memclr4                          0x0800070b   Thumb Code     0  rt_memclr_w.o(.text)
    __aeabi_memclr8                          0x0800070b   Thumb Code     0  rt_memclr_w.o(.text)
    __rt_memclr_w                            0x0800070b   Thumb Code    78  rt_memclr_w.o(.text)
    _memset_w                                0x0800070f   Thumb Code     0  rt_memclr_w.o(.text)
    __use_two_region_memory                  0x08000759   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_escrow$2region                 0x0800075b   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_expand$2region                 0x0800075d   Thumb Code     2  heapauxi.o(.text)
    __read_errno                             0x0800075f   Thumb Code    10  _rserrno.o(.text)
    __set_errno                              0x08000769   Thumb Code    12  _rserrno.o(.text)
    _printf_pre_padding                      0x08000775   Thumb Code    44  _printf_pad.o(.text)
    _printf_post_padding                     0x080007a1   Thumb Code    34  _printf_pad.o(.text)
    _printf_truncate_signed                  0x080007c3   Thumb Code    18  _printf_truncate.o(.text)
    _printf_truncate_unsigned                0x080007d5   Thumb Code    18  _printf_truncate.o(.text)
    _printf_str                              0x080007e7   Thumb Code    82  _printf_str.o(.text)
    _printf_int_dec                          0x08000839   Thumb Code   104  _printf_dec.o(.text)
    _printf_charcount                        0x080008b1   Thumb Code    40  _printf_charcount.o(.text)
    _printf_char_common                      0x080008e3   Thumb Code    32  _printf_char_common.o(.text)
    _sputc                                   0x08000909   Thumb Code    10  _sputc.o(.text)
    _snputc                                  0x08000913   Thumb Code    16  _snputc.o(.text)
    _printf_wctomb                           0x08000925   Thumb Code   182  _printf_wctomb.o(.text)
    _printf_longlong_dec                     0x080009e1   Thumb Code   108  _printf_longlong_dec.o(.text)
    _printf_longlong_oct                     0x08000a5d   Thumb Code    66  _printf_oct_int_ll.o(.text)
    _printf_int_oct                          0x08000a9f   Thumb Code    24  _printf_oct_int_ll.o(.text)
    _printf_ll_oct                           0x08000ab7   Thumb Code    12  _printf_oct_int_ll.o(.text)
    _printf_longlong_hex                     0x08000acd   Thumb Code    86  _printf_hex_int_ll_ptr.o(.text)
    _printf_int_hex                          0x08000b23   Thumb Code    28  _printf_hex_int_ll_ptr.o(.text)
    _printf_ll_hex                           0x08000b3f   Thumb Code    12  _printf_hex_int_ll_ptr.o(.text)
    _printf_hex_ptr                          0x08000b4b   Thumb Code    18  _printf_hex_int_ll_ptr.o(.text)
    __printf                                 0x08000b61   Thumb Code   388  __printf_flags_ss_wp.o(.text)
    _chval                                   0x08000ce9   Thumb Code    28  _chval.o(.text)
    __vfscanf_char                           0x08000d11   Thumb Code    24  scanf_char.o(.text)
    _sgetc                                   0x08000d31   Thumb Code    30  _sgetc.o(.text)
    _sbackspace                              0x08000d4f   Thumb Code    34  _sgetc.o(.text)
    abort                                    0x08000d71   Thumb Code    22  abort.o(.text)
    __assert_puts                            0x08000d87   Thumb Code    20  assert_puts.o(.text)
    __aeabi_memcpy4                          0x08000d9b   Thumb Code     0  rt_memcpy_w.o(.text)
    __aeabi_memcpy8                          0x08000d9b   Thumb Code     0  rt_memcpy_w.o(.text)
    __rt_memcpy_w                            0x08000d9b   Thumb Code   100  rt_memcpy_w.o(.text)
    _memcpy_lastbytes_aligned                0x08000de3   Thumb Code     0  rt_memcpy_w.o(.text)
    _ttywrch                                 0x08000dff   Thumb Code    14  sys_wrch.o(.text)
    _sys_exit                                0x08000e0d   Thumb Code     8  sys_exit.o(.text)
    __aeabi_errno_addr                       0x08000e19   Thumb Code     8  rt_errno_addr_intlibspace.o(.text)
    __errno$intlibspace                      0x08000e19   Thumb Code     0  rt_errno_addr_intlibspace.o(.text)
    __rt_errno_addr$intlibspace              0x08000e19   Thumb Code     0  rt_errno_addr_intlibspace.o(.text)
    _ll_udiv10                               0x08000e21   Thumb Code   138  lludiv10.o(.text)
    isspace                                  0x08000eab   Thumb Code    18  isspace.o(.text)
    _printf_int_common                       0x08000ebd   Thumb Code   178  _printf_intcommon.o(.text)
    __lib_sel_fp_printf                      0x08000f6f   Thumb Code     2  _printf_fp_dec.o(.text)
    _printf_fp_dec_real                      0x08001121   Thumb Code   620  _printf_fp_dec.o(.text)
    _printf_fp_hex_real                      0x0800138d   Thumb Code   756  _printf_fp_hex.o(.text)
    _printf_cs_common                        0x08001689   Thumb Code    20  _printf_char.o(.text)
    _printf_char                             0x0800169d   Thumb Code    16  _printf_char.o(.text)
    _printf_string                           0x080016ad   Thumb Code     8  _printf_char.o(.text)
    _printf_lcs_common                       0x080016b5   Thumb Code    20  _printf_wchar.o(.text)
    _printf_wchar                            0x080016c9   Thumb Code    16  _printf_wchar.o(.text)
    _printf_wstring                          0x080016d9   Thumb Code     8  _printf_wchar.o(.text)
    __vfscanf                                0x080016e1   Thumb Code   880  _scanf.o(.text)
    _wcrtomb                                 0x08001a55   Thumb Code    64  _wcrtomb.o(.text)
    __rt_SIGABRT                             0x08001a95   Thumb Code    14  defsig_abrt_outer.o(.text)
    __user_libspace                          0x08001aa5   Thumb Code     8  libspace.o(.text)
    __user_perproc_libspace                  0x08001aa5   Thumb Code     0  libspace.o(.text)
    __user_perthread_libspace                0x08001aa5   Thumb Code     0  libspace.o(.text)
    __I$use$semihosting                      0x08001aad   Thumb Code     0  use_no_semi.o(.text)
    __use_no_semihosting_swi                 0x08001aad   Thumb Code     2  use_no_semi.o(.text)
    __semihosting_library_function           0x08001aaf   Thumb Code     0  indicate_semi.o(.text)
    __user_setup_stackheap                   0x08001aaf   Thumb Code    74  sys_stackheap_outer.o(.text)
    __rt_ctype_table                         0x08001af9   Thumb Code    16  rt_ctype_table.o(.text)
    __rt_locale                              0x08001b09   Thumb Code     8  rt_locale_intlibspace.o(.text)
    _printf_fp_infnan                        0x08001b11   Thumb Code   112  _printf_fp_infnan.o(.text)
    _btod_etento                             0x08001b91   Thumb Code   224  bigflt0.o(.text)
    exit                                     0x08001c75   Thumb Code    18  exit.o(.text)
    __sig_exit                               0x08001c87   Thumb Code    10  defsig_exit.o(.text)
    __rt_SIGABRT_inner                       0x08001c91   Thumb Code    14  defsig_abrt_inner.o(.text)
    __default_signal_display                 0x08001cc1   Thumb Code    50  defsig_general.o(.text)
    strcmp                                   0x08001cf5   Thumb Code   128  strcmpv7m.o(.text)
    _btod_d2e                                0x08001d75   Thumb Code    62  btod.o(CL$$btod_d2e)
    _d2e_denorm_low                          0x08001db3   Thumb Code    70  btod.o(CL$$btod_d2e_denorm_low)
    _d2e_norm_op1                            0x08001df9   Thumb Code    96  btod.o(CL$$btod_d2e_norm_op1)
    __btod_div_common                        0x08001e59   Thumb Code   696  btod.o(CL$$btod_div_common)
    _e2e                                     0x08002191   Thumb Code   220  btod.o(CL$$btod_e2e)
    _btod_ediv                               0x0800226d   Thumb Code    42  btod.o(CL$$btod_ediv)
    _btod_emul                               0x08002297   Thumb Code    42  btod.o(CL$$btod_emul)
    __btod_mult_common                       0x080022c1   Thumb Code   580  btod.o(CL$$btod_mult_common)
    Angle_PID_control                        0x08002505   Thumb Code   126  pid_app.o(i.Angle_PID_control)
    BusFault_Handler                         0x08002599   Thumb Code     4  stm32f4xx_it.o(i.BusFault_Handler)
    Car_State_Update                         0x0800259d   Thumb Code   326  my_timer.o(i.Car_State_Update)
    DMA1_Stream0_IRQHandler                  0x08002715   Thumb Code    10  stm32f4xx_it.o(i.DMA1_Stream0_IRQHandler)
    DMA2_Stream2_IRQHandler                  0x08002725   Thumb Code    10  stm32f4xx_it.o(i.DMA2_Stream2_IRQHandler)
    DebugMon_Handler                         0x0800291f   Thumb Code     2  stm32f4xx_it.o(i.DebugMon_Handler)
    Delay_us                                 0x08002921   Thumb Code    84  software_iic.o(i.Delay_us)
    EXTI3_IRQHandler                         0x08002979   Thumb Code    10  stm32f4xx_it.o(i.EXTI3_IRQHandler)
    Encoder_Driver_Init                      0x08002985   Thumb Code    42  encoder_driver.o(i.Encoder_Driver_Init)
    Encoder_Driver_Update                    0x080029b5   Thumb Code   126  encoder_driver.o(i.Encoder_Driver_Update)
    Encoder_Init                             0x08002a45   Thumb Code    24  encoder_app.o(i.Encoder_Init)
    Encoder_Task                             0x08002a6d   Thumb Code    16  encoder_app.o(i.Encoder_Task)
    Error_Handler                            0x08002a85   Thumb Code     6  main.o(i.Error_Handler)
    Gray_Init                                0x08002ab5   Thumb Code    28  gray_app.o(i.Gray_Init)
    Gray_Task                                0x08002b21   Thumb Code   108  gray_app.o(i.Gray_Task)
    HAL_DMA_Abort                            0x08002b9d   Thumb Code   172  stm32f4xx_hal_dma.o(i.HAL_DMA_Abort)
    HAL_DMA_Abort_IT                         0x08002c49   Thumb Code    40  stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT)
    HAL_DMA_IRQHandler                       0x08002c71   Thumb Code   570  stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler)
    HAL_DMA_Init                             0x08002eb1   Thumb Code   232  stm32f4xx_hal_dma.o(i.HAL_DMA_Init)
    HAL_DMA_Start_IT                         0x08002f9d   Thumb Code   146  stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT)
    HAL_Delay                                0x08003031   Thumb Code    36  stm32f4xx_hal.o(i.HAL_Delay)
    HAL_GPIO_EXTI_Callback                   0x08003059   Thumb Code     2  stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback)
    HAL_GPIO_EXTI_IRQHandler                 0x0800305d   Thumb Code    24  stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler)
    HAL_GPIO_Init                            0x08003079   Thumb Code   454  stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init)
    HAL_GPIO_ReadPin                         0x0800326d   Thumb Code    16  stm32f4xx_hal_gpio.o(i.HAL_GPIO_ReadPin)
    HAL_GPIO_WritePin                        0x0800327d   Thumb Code    12  stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin)
    HAL_GetTick                              0x08003289   Thumb Code     6  stm32f4xx_hal.o(i.HAL_GetTick)
    HAL_I2C_Init                             0x08003295   Thumb Code   446  stm32f4xx_hal_i2c.o(i.HAL_I2C_Init)
    HAL_I2C_Mem_Read                         0x08003465   Thumb Code   762  stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read)
    HAL_I2C_Mem_Write                        0x0800376d   Thumb Code   348  stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write)
    HAL_I2C_MspInit                          0x080038d1   Thumb Code   228  i2c.o(i.HAL_I2C_MspInit)
    HAL_IncTick                              0x080039c5   Thumb Code    16  stm32f4xx_hal.o(i.HAL_IncTick)
    HAL_Init                                 0x080039dd   Thumb Code    54  stm32f4xx_hal.o(i.HAL_Init)
    HAL_InitTick                             0x08003a19   Thumb Code    64  stm32f4xx_hal.o(i.HAL_InitTick)
    HAL_MspInit                              0x08003a65   Thumb Code    74  stm32f4xx_hal_msp.o(i.HAL_MspInit)
    HAL_NVIC_EnableIRQ                       0x08003ab5   Thumb Code    40  stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ)
    HAL_NVIC_SetPriority                     0x08003add   Thumb Code   124  stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority)
    HAL_NVIC_SetPriorityGrouping             0x08003b59   Thumb Code    32  stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping)
    HAL_RCC_ClockConfig                      0x08003b81   Thumb Code   368  stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig)
    HAL_RCC_GetHCLKFreq                      0x08003d05   Thumb Code     6  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq)
    HAL_RCC_GetPCLK1Freq                     0x08003d11   Thumb Code    22  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq)
    HAL_RCC_GetPCLK2Freq                     0x08003d31   Thumb Code    22  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq)
    HAL_RCC_GetSysClockFreq                  0x08003d51   Thumb Code   162  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq)
    HAL_RCC_OscConfig                        0x08003e01   Thumb Code  1172  stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig)
    HAL_SYSTICK_Config                       0x0800429d   Thumb Code    52  stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Config)
    HAL_TIMEx_BreakCallback                  0x080042d1   Thumb Code     2  stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_BreakCallback)
    HAL_TIMEx_CommutCallback                 0x080042d3   Thumb Code     2  stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback)
    HAL_TIMEx_ConfigBreakDeadTime            0x080042d5   Thumb Code   116  stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigBreakDeadTime)
    HAL_TIMEx_MasterConfigSynchronization    0x08004349   Thumb Code   150  stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization)
    HAL_TIM_Base_Init                        0x080043fd   Thumb Code   102  stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init)
    HAL_TIM_Base_MspInit                     0x08004465   Thumb Code   106  tim.o(i.HAL_TIM_Base_MspInit)
    HAL_TIM_Base_Start_IT                    0x080044d9   Thumb Code   138  stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_IT)
    HAL_TIM_ConfigClockSource                0x08004581   Thumb Code   268  stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource)
    HAL_TIM_Encoder_Init                     0x0800468d   Thumb Code   200  stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Init)
    HAL_TIM_Encoder_MspInit                  0x08004755   Thumb Code   226  tim.o(i.HAL_TIM_Encoder_MspInit)
    HAL_TIM_Encoder_Start                    0x0800484d   Thumb Code   204  stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start)
    HAL_TIM_IC_CaptureCallback               0x08004919   Thumb Code     2  stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback)
    HAL_TIM_IRQHandler                       0x0800491b   Thumb Code   364  stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler)
    HAL_TIM_MspPostInit                      0x08004a89   Thumb Code    86  tim.o(i.HAL_TIM_MspPostInit)
    HAL_TIM_OC_DelayElapsedCallback          0x08004aed   Thumb Code     2  stm32f4xx_hal_tim.o(i.HAL_TIM_OC_DelayElapsedCallback)
    HAL_TIM_PWM_ConfigChannel                0x08004aef   Thumb Code   260  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel)
    HAL_TIM_PWM_Init                         0x08004bf3   Thumb Code   102  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Init)
    HAL_TIM_PWM_MspInit                      0x08004c59   Thumb Code     2  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_MspInit)
    HAL_TIM_PWM_PulseFinishedCallback        0x08004c5b   Thumb Code     2  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback)
    HAL_TIM_PWM_Start                        0x08004c5d   Thumb Code   238  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start)
    HAL_TIM_PeriodElapsedCallback            0x08004d69   Thumb Code   274  my_timer.o(i.HAL_TIM_PeriodElapsedCallback)
    HAL_TIM_TriggerCallback                  0x08004ead   Thumb Code     2  stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerCallback)
    HAL_UARTEx_ReceiveToIdle_DMA             0x08004eaf   Thumb Code   112  stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA)
    HAL_UARTEx_RxEventCallback               0x08004f21   Thumb Code    66  usart_app.o(i.HAL_UARTEx_RxEventCallback)
    HAL_UART_DMAStop                         0x08004f79   Thumb Code   138  stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop)
    HAL_UART_ErrorCallback                   0x08005003   Thumb Code     2  stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback)
    HAL_UART_IRQHandler                      0x08005005   Thumb Code   772  stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler)
    HAL_UART_Init                            0x0800530d   Thumb Code   118  stm32f4xx_hal_uart.o(i.HAL_UART_Init)
    HAL_UART_MspInit                         0x08005385   Thumb Code   440  usart.o(i.HAL_UART_MspInit)
    HAL_UART_RxCpltCallback                  0x08005565   Thumb Code     2  stm32f4xx_hal_uart.o(i.HAL_UART_RxCpltCallback)
    HAL_UART_RxHalfCpltCallback              0x08005567   Thumb Code     2  stm32f4xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback)
    HAL_UART_Transmit                        0x08005569   Thumb Code   190  stm32f4xx_hal_uart.o(i.HAL_UART_Transmit)
    HAL_UART_TxCpltCallback                  0x08005627   Thumb Code     2  stm32f4xx_hal_uart.o(i.HAL_UART_TxCpltCallback)
    HardFault_Handler                        0x08005629   Thumb Code     4  stm32f4xx_it.o(i.HardFault_Handler)
    IIC_Get_Digtal                           0x08005bc5   Thumb Code    32  software_iic.o(i.IIC_Get_Digtal)
    IIC_ReadBytes                            0x08005be5   Thumb Code   118  software_iic.o(i.IIC_ReadBytes)
    IIC_RecvByte                             0x08005c5d   Thumb Code   120  software_iic.o(i.IIC_RecvByte)
    IIC_SendAck                              0x08005cd9   Thumb Code    48  software_iic.o(i.IIC_SendAck)
    IIC_SendByte                             0x08005d0d   Thumb Code    82  software_iic.o(i.IIC_SendByte)
    IIC_SendNAck                             0x08005d65   Thumb Code    38  software_iic.o(i.IIC_SendNAck)
    IIC_Start                                0x08005d91   Thumb Code    52  software_iic.o(i.IIC_Start)
    IIC_Stop                                 0x08005dc9   Thumb Code    46  software_iic.o(i.IIC_Stop)
    IIC_WaitAck                              0x08005dfd   Thumb Code    54  software_iic.o(i.IIC_WaitAck)
    Line_PID_control                         0x08005e39   Thumb Code   112  pid_app.o(i.Line_PID_control)
    MPU6050_CalculateEuler                   0x08005ec1   Thumb Code   280  mpu6050_hal.o(i.MPU6050_CalculateEuler)
    MPU6050_CalibrateGyro                    0x08005ff1   Thumb Code   220  mpu6050_hal.o(i.MPU6050_CalibrateGyro)
    MPU6050_Config                           0x080060e1   Thumb Code   102  mpu6050_hal.o(i.MPU6050_Config)
    MPU6050_Init                             0x0800614d   Thumb Code   122  mpu6050_hal.o(i.MPU6050_Init)
    MPU6050_ReadData                         0x080061d5   Thumb Code   250  mpu6050_hal.o(i.MPU6050_ReadData)
    MPU6050_ReadRawData                      0x080062ed   Thumb Code   148  mpu6050_hal.o(i.MPU6050_ReadRawData)
    MPU6050_Reset                            0x080063f1   Thumb Code    12  mpu6050_hal.o(i.MPU6050_Reset)
    MPU6050_TestConnection                   0x080063fd   Thumb Code    36  mpu6050_hal.o(i.MPU6050_TestConnection)
    MX_DMA_Init                              0x08006531   Thumb Code    98  dma.o(i.MX_DMA_Init)
    MX_GPIO_Init                             0x08006599   Thumb Code   440  gpio.o(i.MX_GPIO_Init)
    MX_I2C1_Init                             0x08006765   Thumb Code    48  i2c.o(i.MX_I2C1_Init)
    MX_I2C2_Init                             0x080067a1   Thumb Code    48  i2c.o(i.MX_I2C2_Init)
    MX_TIM1_Init                             0x080067dd   Thumb Code   254  tim.o(i.MX_TIM1_Init)
    MX_TIM2_Init                             0x080068e5   Thumb Code   102  tim.o(i.MX_TIM2_Init)
    MX_TIM3_Init                             0x08006951   Thumb Code   120  tim.o(i.MX_TIM3_Init)
    MX_TIM4_Init                             0x080069d1   Thumb Code   120  tim.o(i.MX_TIM4_Init)
    MX_UART5_Init                            0x08006a51   Thumb Code    46  usart.o(i.MX_UART5_Init)
    MX_USART1_UART_Init                      0x08006a89   Thumb Code    46  usart.o(i.MX_USART1_UART_Init)
    MemManage_Handler                        0x08006ac1   Thumb Code     4  stm32f4xx_it.o(i.MemManage_Handler)
    Motor_Create                             0x08006ac5   Thumb Code   190  motor_driver.o(i.Motor_Create)
    Motor_Init                               0x08006b89   Thumb Code    80  motor_app.o(i.Motor_Init)
    Motor_SetSpeed                           0x08006be9   Thumb Code   160  motor_driver.o(i.Motor_SetSpeed)
    NMI_Handler                              0x08006cd3   Thumb Code     4  stm32f4xx_it.o(i.NMI_Handler)
    OLED_Clear                               0x08006cd7   Thumb Code    56  oled.o(i.OLED_Clear)
    OLED_Init                                0x08006d11   Thumb Code    42  oled.o(i.OLED_Init)
    OLED_Set_Position                        0x08006d41   Thumb Code    36  oled.o(i.OLED_Set_Position)
    OLED_ShowChar                            0x08006d65   Thumb Code   148  oled.o(i.OLED_ShowChar)
    OLED_ShowStr                             0x08006e01   Thumb Code    58  oled.o(i.OLED_ShowStr)
    OLED_Write_cmd                           0x08006e3d   Thumb Code    32  oled.o(i.OLED_Write_cmd)
    OLED_Write_data                          0x08006e61   Thumb Code    32  oled.o(i.OLED_Write_data)
    PID_Init                                 0x08006e85   Thumb Code   232  pid_app.o(i.PID_Init)
    PID_Task                                 0x08006f95   Thumb Code   272  pid_app.o(i.PID_Task)
    PendSV_Handler                           0x080070e9   Thumb Code     2  stm32f4xx_it.o(i.PendSV_Handler)
    Ping                                     0x080070eb   Thumb Code    30  software_iic.o(i.Ping)
    SVC_Handler                              0x08007109   Thumb Code     2  stm32f4xx_it.o(i.SVC_Handler)
    SysTick_Handler                          0x080071c9   Thumb Code     8  stm32f4xx_it.o(i.SysTick_Handler)
    SystemClock_Config                       0x080071d1   Thumb Code   170  main.o(i.SystemClock_Config)
    SystemInit                               0x08007285   Thumb Code    14  system_stm32f4xx.o(i.SystemInit)
    TIM2_IRQHandler                          0x08007299   Thumb Code    10  stm32f4xx_it.o(i.TIM2_IRQHandler)
    TIM_Base_SetConfig                       0x080072a9   Thumb Code   178  stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig)
    TIM_CCxChannelCmd                        0x08007389   Thumb Code    34  stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd)
    TIM_ETR_SetConfig                        0x080073ab   Thumb Code    22  stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig)
    TIM_OC2_SetConfig                        0x08007445   Thumb Code   114  stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig)
    UART5_IRQHandler                         0x080075dd   Thumb Code    10  stm32f4xx_it.o(i.UART5_IRQHandler)
    UART_Start_Receive_DMA                   0x08007b01   Thumb Code   202  stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA)
    USART1_IRQHandler                        0x08007cc5   Thumb Code    10  stm32f4xx_it.o(i.USART1_IRQHandler)
    UsageFault_Handler                       0x08007cd5   Thumb Code     4  stm32f4xx_it.o(i.UsageFault_Handler)
    __ARM_fpclassify                         0x08007cd9   Thumb Code    48  fpclassify.o(i.__ARM_fpclassify)
    __ARM_fpclassifyf                        0x08007d09   Thumb Code    38  fpclassifyf.o(i.__ARM_fpclassifyf)
    __hardfp_atan2f                          0x08007d69   Thumb Code   594  atan2f.o(i.__hardfp_atan2f)
    __hardfp_roundf                          0x08008015   Thumb Code   154  roundf.o(i.__hardfp_roundf)
    __hardfp_sqrtf                           0x080080af   Thumb Code    58  sqrtf.o(i.__hardfp_sqrtf)
    __mathlib_flt_infnan2                    0x080080e9   Thumb Code     6  funder.o(i.__mathlib_flt_infnan2)
    __mathlib_flt_underflow                  0x080080f1   Thumb Code    10  funder.o(i.__mathlib_flt_underflow)
    _is_digit                                0x08008101   Thumb Code    14  __printf_wp.o(i._is_digit)
    all_task_init                            0x08008111   Thumb Code    46  my_scheduler.o(i.all_task_init)
    all_task_run                             0x08008145   Thumb Code    78  my_scheduler.o(i.all_task_run)
    bno080_task                              0x0800819d   Thumb Code   122  mpu6050_app.o(i.bno080_task)
    convert_to_continuous_yaw                0x0800823d   Thumb Code   126  mpu6050_app.o(i.convert_to_continuous_yaw)
    get_yaw                                  0x080082d5   Thumb Code    22  mpu6050_app.o(i.get_yaw)
    key_read                                 0x080082f1   Thumb Code    68  key_driver.o(i.key_read)
    key_task                                 0x0800833d   Thumb Code   120  key_app.o(i.key_task)
    led_disp                                 0x080083c9   Thumb Code   120  led_driver.o(i.led_disp)
    led_init                                 0x0800844d   Thumb Code     2  led_app.o(i.led_init)
    led_task                                 0x08008451   Thumb Code    10  led_app.o(i.led_task)
    main                                     0x08008461   Thumb Code    60  main.o(i.main)
    motor_set_l                              0x0800849d   Thumb Code    26  motor_app.o(i.motor_set_l)
    motor_set_r                              0x080084bd   Thumb Code    26  motor_app.o(i.motor_set_r)
    my_bno080_init                           0x080084dd   Thumb Code   164  mpu6050_app.o(i.my_bno080_init)
    my_oled_init                             0x080086cd   Thumb Code     8  oled_app.o(i.my_oled_init)
    my_printf                                0x080086d5   Thumb Code    58  usart_app.o(i.my_printf)
    oled_printf                              0x0800870f   Thumb Code    60  oled_app.o(i.oled_printf)
    oled_task                                0x0800874d   Thumb Code   148  oled_app.o(i.oled_task)
    pid_calculate_positional                 0x08008811   Thumb Code    38  pid.o(i.pid_calculate_positional)
    pid_constrain                            0x08008837   Thumb Code    42  pid.o(i.pid_constrain)
    pid_init                                 0x080088dd   Thumb Code    78  pid.o(i.pid_init)
    pid_reset                                0x08008971   Thumb Code    58  pid.o(i.pid_reset)
    pid_set_target                           0x080089b1   Thumb Code     6  pid.o(i.pid_set_target)
    rt_ringbuffer_data_len                   0x080089b7   Thumb Code    60  ringbuffer.o(i.rt_ringbuffer_data_len)
    rt_ringbuffer_get                        0x080089f5   Thumb Code   180  ringbuffer.o(i.rt_ringbuffer_get)
    rt_ringbuffer_init                       0x08008ae1   Thumb Code    84  ringbuffer.o(i.rt_ringbuffer_init)
    rt_ringbuffer_put                        0x08008b75   Thumb Code   182  ringbuffer.o(i.rt_ringbuffer_put)
    timer_init                               0x08008c91   Thumb Code    10  my_timer.o(i.timer_init)
    uart_init                                0x08008ca1   Thumb Code    40  usart_app.o(i.uart_init)
    uart_task                                0x08008cdd   Thumb Code   168  usart_app.o(i.uart_task)
    _get_lc_numeric                          0x08008dd5   Thumb Code    44  lc_numeric_c.o(locale$$code)
    _get_lc_ctype                            0x08008e01   Thumb Code    44  lc_ctype_c.o(locale$$code)
    __aeabi_d2f                              0x08008e2d   Thumb Code     0  d2f.o(x$fpl$d2f)
    _d2f                                     0x08008e2d   Thumb Code    98  d2f.o(x$fpl$d2f)
    __aeabi_ddiv                             0x08008e91   Thumb Code     0  ddiv.o(x$fpl$ddiv)
    _ddiv                                    0x08008e91   Thumb Code   552  ddiv.o(x$fpl$ddiv)
    __fpl_dnaninf                            0x08009141   Thumb Code   156  dnaninf.o(x$fpl$dnaninf)
    __fpl_dretinf                            0x080091dd   Thumb Code    12  dretinf.o(x$fpl$dretinf)
    __aeabi_f2d                              0x080091e9   Thumb Code     0  f2d.o(x$fpl$f2d)
    _f2d                                     0x080091e9   Thumb Code    86  f2d.o(x$fpl$f2d)
    __fpl_fnaninf                            0x0800923f   Thumb Code   140  fnaninf.o(x$fpl$fnaninf)
    _fp_init                                 0x080092cb   Thumb Code    10  fpinit.o(x$fpl$fpinit)
    __fplib_config_fpu_vfp                   0x080092d3   Thumb Code     0  fpinit.o(x$fpl$fpinit)
    __fplib_config_pureend_doubles           0x080092d3   Thumb Code     0  fpinit.o(x$fpl$fpinit)
    __fpl_fretinf                            0x080092d5   Thumb Code    10  fretinf.o(x$fpl$fretinf)
    _frnd                                    0x080092e1   Thumb Code    96  frnd.o(x$fpl$frnd)
    _printf_fp_dec                           0x08009341   Thumb Code     4  printf1.o(x$fpl$printf1)
    _printf_fp_hex                           0x08009345   Thumb Code     4  printf2.o(x$fpl$printf2)
    __I$use$fp                               0x08009348   Number         0  usenofp.o(x$fpl$usenofp)
    AHBPrescTable                            0x08009350   Data          16  system_stm32f4xx.o(.constdata)
    APBPrescTable                            0x08009360   Data           8  system_stm32f4xx.o(.constdata)
    F6X8                                     0x08009368   Data         552  oled.o(.constdata)
    Hzk                                      0x08009b80   Data         128  oled.o(.constdata)
    Hzb                                      0x08009c00   Data         512  oled.o(.constdata)
    Region$$Table$$Base                      0x08009f00   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x08009f20   Number         0  anon$$obj.o(Region$$Table)
    __ctype                                  0x08009f49   Data           0  lc_ctype_c.o(locale$$data)
    uwTick                                   0x20000000   Data           4  stm32f4xx_hal.o(.data)
    uwTickPrio                               0x20000004   Data           4  stm32f4xx_hal.o(.data)
    uwTickFreq                               0x20000008   Data           1  stm32f4xx_hal.o(.data)
    SystemCoreClock                          0x2000000c   Data           4  system_stm32f4xx.o(.data)
    initcmd1                                 0x20000010   Data          22  oled.o(.data)
    led_rgb                                  0x20000055   Data           5  led_app.o(.data)
    runing_flat                              0x2000005a   Data           1  key_app.o(.data)
    runing_time                              0x2000005c   Data           2  key_app.o(.data)
    test                                     0x2000005e   Data           1  key_app.o(.data)
    basic_speed                              0x20000060   Data           4  pid_app.o(.data)
    pid_params_left                          0x20000064   Data          20  pid_app.o(.data)
    pid_params_right                         0x20000078   Data          20  pid_app.o(.data)
    pid_params_line                          0x2000008c   Data          20  pid_app.o(.data)
    pid_params_angle                         0x200000a0   Data          20  pid_app.o(.data)
    pid_running                              0x200000b4   Data           1  pid_app.o(.data)
    pid_control_mode                         0x200000b5   Data           1  pid_app.o(.data)
    stop_flat                                0x200000b6   Data           1  pid_app.o(.data)
    angle_flat                               0x200000b7   Data           1  pid_app.o(.data)
    Digtal                                   0x200000b8   Data           1  gray_app.o(.data)
    gray_weights                             0x200000bc   Data          32  gray_app.o(.data)
    g_line_position_error                    0x200000dc   Data           4  gray_app.o(.data)
    roll                                     0x200000e8   Data           4  mpu6050_app.o(.data)
    pitch                                    0x200000ec   Data           4  mpu6050_app.o(.data)
    yaw                                      0x200000f0   Data           4  mpu6050_app.o(.data)
    first_flat                               0x200000f4   Data           1  mpu6050_app.o(.data)
    frist_yaw                                0x200000f8   Data           4  mpu6050_app.o(.data)
    g_last_yaw                               0x200000fc   Data           4  mpu6050_app.o(.data)
    g_revolution_count                       0x20000100   Data           4  mpu6050_app.o(.data)
    g_is_yaw_initialized                     0x20000104   Data           1  mpu6050_app.o(.data)
    all_task                                 0x20000108   Data          60  my_scheduler.o(.data)
    task_num                                 0x20000144   Data           1  my_scheduler.o(.data)
    measure_timer5ms                         0x20000148   Data           1  my_timer.o(.data)
    key_timer10ms                            0x20000149   Data           1  my_timer.o(.data)
    output_ff_flag                           0x2000014a   Data           1  my_timer.o(.data)
    output_timer500ms                        0x2000014c   Data           4  my_timer.o(.data)
    intput_ff_flag                           0x20000150   Data           1  my_timer.o(.data)
    intput_timer500ms                        0x20000154   Data           4  my_timer.o(.data)
    led_timer500ms                           0x20000158   Data           4  my_timer.o(.data)
    point_count                              0x2000015c   Data           1  my_timer.o(.data)
    system_mode                              0x2000015d   Data           1  my_timer.o(.data)
    circle_count                             0x2000015e   Data           1  my_timer.o(.data)
    distance                                 0x20000160   Data           4  my_timer.o(.data)
    hi2c1                                    0x20000164   Data          84  i2c.o(.bss)
    hi2c2                                    0x200001b8   Data          84  i2c.o(.bss)
    htim1                                    0x2000020c   Data          72  tim.o(.bss)
    htim2                                    0x20000254   Data          72  tim.o(.bss)
    htim3                                    0x2000029c   Data          72  tim.o(.bss)
    htim4                                    0x200002e4   Data          72  tim.o(.bss)
    huart5                                   0x2000032c   Data          72  usart.o(.bss)
    huart1                                   0x20000374   Data          72  usart.o(.bss)
    hdma_uart5_rx                            0x200003bc   Data          96  usart.o(.bss)
    hdma_usart1_rx                           0x2000041c   Data          96  usart.o(.bss)
    uart_rx_dma_buffer                       0x2000047c   Data         128  usart_app.o(.bss)
    uart_dma_buffer                          0x200004fc   Data         128  usart_app.o(.bss)
    uart_ringbuffer                          0x2000057c   Data          12  usart_app.o(.bss)
    ringbuffer_pool                          0x20000588   Data         128  usart_app.o(.bss)
    right_motor                              0x20000608   Data          48  motor_app.o(.bss)
    left_motor                               0x20000638   Data          48  motor_app.o(.bss)
    left_encoder                             0x20000668   Data          16  encoder_app.o(.bss)
    right_encoder                            0x20000678   Data          16  encoder_app.o(.bss)
    pid_speed_left                           0x20000688   Data          60  pid_app.o(.bss)
    pid_speed_right                          0x200006c4   Data          60  pid_app.o(.bss)
    pid_line                                 0x20000700   Data          60  pid_app.o(.bss)
    pid_angle                                0x2000073c   Data          60  pid_app.o(.bss)
    __libspace_start                         0x200007a0   Data          96  libspace.o(.bss)
    __temporary_stack_top$libspace           0x20000800   Data           0  libspace.o(.bss)



==============================================================================

Memory Map of the image

  Image Entry point : 0x08000189

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x0000a1b0, Max: 0x00080000, ABSOLUTE, COMPRESSED[0x0000a0f0])

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x0000a04c, Max: 0x00080000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x00000188   Data   RO            3    RESET               startup_stm32f407xx.o
    0x08000188   0x08000188   0x00000008   Code   RO         5015  * !!!main             c_w.l(__main.o)
    0x08000190   0x08000190   0x00000034   Code   RO         5441    !!!scatter          c_w.l(__scatter.o)
    0x080001c4   0x080001c4   0x0000005a   Code   RO         5439    !!dczerorl2         c_w.l(__dczerorl2.o)
    0x0800021e   0x0800021e   0x00000002   PAD
    0x08000220   0x08000220   0x0000001c   Code   RO         5443    !!handler_zi        c_w.l(__scatter_zi.o)
    0x0800023c   0x0800023c   0x00000000   Code   RO         5143    .ARM.Collect$$_printf_percent$$00000000  c_w.l(_printf_percent.o)
    0x0800023c   0x0800023c   0x00000006   Code   RO         5132    .ARM.Collect$$_printf_percent$$00000001  c_w.l(_printf_n.o)
    0x08000242   0x08000242   0x00000006   Code   RO         5134    .ARM.Collect$$_printf_percent$$00000002  c_w.l(_printf_p.o)
    0x08000248   0x08000248   0x00000006   Code   RO         5139    .ARM.Collect$$_printf_percent$$00000003  c_w.l(_printf_f.o)
    0x0800024e   0x0800024e   0x00000006   Code   RO         5140    .ARM.Collect$$_printf_percent$$00000004  c_w.l(_printf_e.o)
    0x08000254   0x08000254   0x00000006   Code   RO         5141    .ARM.Collect$$_printf_percent$$00000005  c_w.l(_printf_g.o)
    0x0800025a   0x0800025a   0x00000006   Code   RO         5142    .ARM.Collect$$_printf_percent$$00000006  c_w.l(_printf_a.o)
    0x08000260   0x08000260   0x0000000a   Code   RO         5147    .ARM.Collect$$_printf_percent$$00000007  c_w.l(_printf_ll.o)
    0x0800026a   0x0800026a   0x00000006   Code   RO         5136    .ARM.Collect$$_printf_percent$$00000008  c_w.l(_printf_i.o)
    0x08000270   0x08000270   0x00000006   Code   RO         5137    .ARM.Collect$$_printf_percent$$00000009  c_w.l(_printf_d.o)
    0x08000276   0x08000276   0x00000006   Code   RO         5138    .ARM.Collect$$_printf_percent$$0000000A  c_w.l(_printf_u.o)
    0x0800027c   0x0800027c   0x00000006   Code   RO         5135    .ARM.Collect$$_printf_percent$$0000000B  c_w.l(_printf_o.o)
    0x08000282   0x08000282   0x00000006   Code   RO         5133    .ARM.Collect$$_printf_percent$$0000000C  c_w.l(_printf_x.o)
    0x08000288   0x08000288   0x00000006   Code   RO         5144    .ARM.Collect$$_printf_percent$$0000000D  c_w.l(_printf_lli.o)
    0x0800028e   0x0800028e   0x00000006   Code   RO         5145    .ARM.Collect$$_printf_percent$$0000000E  c_w.l(_printf_lld.o)
    0x08000294   0x08000294   0x00000006   Code   RO         5146    .ARM.Collect$$_printf_percent$$0000000F  c_w.l(_printf_llu.o)
    0x0800029a   0x0800029a   0x00000006   Code   RO         5151    .ARM.Collect$$_printf_percent$$00000010  c_w.l(_printf_llo.o)
    0x080002a0   0x080002a0   0x00000006   Code   RO         5152    .ARM.Collect$$_printf_percent$$00000011  c_w.l(_printf_llx.o)
    0x080002a6   0x080002a6   0x0000000a   Code   RO         5148    .ARM.Collect$$_printf_percent$$00000012  c_w.l(_printf_l.o)
    0x080002b0   0x080002b0   0x00000006   Code   RO         5130    .ARM.Collect$$_printf_percent$$00000013  c_w.l(_printf_c.o)
    0x080002b6   0x080002b6   0x00000006   Code   RO         5131    .ARM.Collect$$_printf_percent$$00000014  c_w.l(_printf_s.o)
    0x080002bc   0x080002bc   0x00000006   Code   RO         5149    .ARM.Collect$$_printf_percent$$00000015  c_w.l(_printf_lc.o)
    0x080002c2   0x080002c2   0x00000006   Code   RO         5150    .ARM.Collect$$_printf_percent$$00000016  c_w.l(_printf_ls.o)
    0x080002c8   0x080002c8   0x00000004   Code   RO         5243    .ARM.Collect$$_printf_percent$$00000017  c_w.l(_printf_percent_end.o)
    0x080002cc   0x080002cc   0x00000002   Code   RO         5314    .ARM.Collect$$libinit$$00000000  c_w.l(libinit.o)
    0x080002ce   0x080002ce   0x00000004   Code   RO         5332    .ARM.Collect$$libinit$$00000001  c_w.l(libinit2.o)
    0x080002d2   0x080002d2   0x00000000   Code   RO         5335    .ARM.Collect$$libinit$$00000004  c_w.l(libinit2.o)
    0x080002d2   0x080002d2   0x00000000   Code   RO         5338    .ARM.Collect$$libinit$$0000000A  c_w.l(libinit2.o)
    0x080002d2   0x080002d2   0x00000000   Code   RO         5340    .ARM.Collect$$libinit$$0000000C  c_w.l(libinit2.o)
    0x080002d2   0x080002d2   0x00000000   Code   RO         5342    .ARM.Collect$$libinit$$0000000E  c_w.l(libinit2.o)
    0x080002d2   0x080002d2   0x00000006   Code   RO         5343    .ARM.Collect$$libinit$$0000000F  c_w.l(libinit2.o)
    0x080002d8   0x080002d8   0x00000000   Code   RO         5345    .ARM.Collect$$libinit$$00000011  c_w.l(libinit2.o)
    0x080002d8   0x080002d8   0x0000000c   Code   RO         5346    .ARM.Collect$$libinit$$00000012  c_w.l(libinit2.o)
    0x080002e4   0x080002e4   0x00000000   Code   RO         5347    .ARM.Collect$$libinit$$00000013  c_w.l(libinit2.o)
    0x080002e4   0x080002e4   0x00000000   Code   RO         5349    .ARM.Collect$$libinit$$00000015  c_w.l(libinit2.o)
    0x080002e4   0x080002e4   0x0000000a   Code   RO         5350    .ARM.Collect$$libinit$$00000016  c_w.l(libinit2.o)
    0x080002ee   0x080002ee   0x00000000   Code   RO         5351    .ARM.Collect$$libinit$$00000017  c_w.l(libinit2.o)
    0x080002ee   0x080002ee   0x00000000   Code   RO         5353    .ARM.Collect$$libinit$$00000019  c_w.l(libinit2.o)
    0x080002ee   0x080002ee   0x00000000   Code   RO         5355    .ARM.Collect$$libinit$$0000001B  c_w.l(libinit2.o)
    0x080002ee   0x080002ee   0x00000000   Code   RO         5357    .ARM.Collect$$libinit$$0000001D  c_w.l(libinit2.o)
    0x080002ee   0x080002ee   0x00000000   Code   RO         5359    .ARM.Collect$$libinit$$0000001F  c_w.l(libinit2.o)
    0x080002ee   0x080002ee   0x00000000   Code   RO         5361    .ARM.Collect$$libinit$$00000021  c_w.l(libinit2.o)
    0x080002ee   0x080002ee   0x00000000   Code   RO         5363    .ARM.Collect$$libinit$$00000023  c_w.l(libinit2.o)
    0x080002ee   0x080002ee   0x00000000   Code   RO         5365    .ARM.Collect$$libinit$$00000025  c_w.l(libinit2.o)
    0x080002ee   0x080002ee   0x00000000   Code   RO         5369    .ARM.Collect$$libinit$$0000002C  c_w.l(libinit2.o)
    0x080002ee   0x080002ee   0x00000000   Code   RO         5371    .ARM.Collect$$libinit$$0000002E  c_w.l(libinit2.o)
    0x080002ee   0x080002ee   0x00000000   Code   RO         5373    .ARM.Collect$$libinit$$00000030  c_w.l(libinit2.o)
    0x080002ee   0x080002ee   0x00000000   Code   RO         5375    .ARM.Collect$$libinit$$00000032  c_w.l(libinit2.o)
    0x080002ee   0x080002ee   0x00000002   Code   RO         5376    .ARM.Collect$$libinit$$00000033  c_w.l(libinit2.o)
    0x080002f0   0x080002f0   0x00000002   Code   RO         5410    .ARM.Collect$$libshutdown$$00000000  c_w.l(libshutdown.o)
    0x080002f2   0x080002f2   0x00000000   Code   RO         5420    .ARM.Collect$$libshutdown$$00000002  c_w.l(libshutdown2.o)
    0x080002f2   0x080002f2   0x00000000   Code   RO         5422    .ARM.Collect$$libshutdown$$00000004  c_w.l(libshutdown2.o)
    0x080002f2   0x080002f2   0x00000000   Code   RO         5424    .ARM.Collect$$libshutdown$$00000006  c_w.l(libshutdown2.o)
    0x080002f2   0x080002f2   0x00000000   Code   RO         5427    .ARM.Collect$$libshutdown$$00000009  c_w.l(libshutdown2.o)
    0x080002f2   0x080002f2   0x00000000   Code   RO         5430    .ARM.Collect$$libshutdown$$0000000C  c_w.l(libshutdown2.o)
    0x080002f2   0x080002f2   0x00000000   Code   RO         5432    .ARM.Collect$$libshutdown$$0000000E  c_w.l(libshutdown2.o)
    0x080002f2   0x080002f2   0x00000000   Code   RO         5435    .ARM.Collect$$libshutdown$$00000011  c_w.l(libshutdown2.o)
    0x080002f2   0x080002f2   0x00000002   Code   RO         5436    .ARM.Collect$$libshutdown$$00000012  c_w.l(libshutdown2.o)
    0x080002f4   0x080002f4   0x00000000   Code   RO         5053    .ARM.Collect$$rtentry$$00000000  c_w.l(__rtentry.o)
    0x080002f4   0x080002f4   0x00000000   Code   RO         5205    .ARM.Collect$$rtentry$$00000002  c_w.l(__rtentry2.o)
    0x080002f4   0x080002f4   0x00000006   Code   RO         5217    .ARM.Collect$$rtentry$$00000004  c_w.l(__rtentry4.o)
    0x080002fa   0x080002fa   0x00000000   Code   RO         5207    .ARM.Collect$$rtentry$$00000009  c_w.l(__rtentry2.o)
    0x080002fa   0x080002fa   0x00000004   Code   RO         5208    .ARM.Collect$$rtentry$$0000000A  c_w.l(__rtentry2.o)
    0x080002fe   0x080002fe   0x00000000   Code   RO         5210    .ARM.Collect$$rtentry$$0000000C  c_w.l(__rtentry2.o)
    0x080002fe   0x080002fe   0x00000008   Code   RO         5211    .ARM.Collect$$rtentry$$0000000D  c_w.l(__rtentry2.o)
    0x08000306   0x08000306   0x00000002   Code   RO         5319    .ARM.Collect$$rtexit$$00000000  c_w.l(rtexit.o)
    0x08000308   0x08000308   0x00000000   Code   RO         5378    .ARM.Collect$$rtexit$$00000002  c_w.l(rtexit2.o)
    0x08000308   0x08000308   0x00000004   Code   RO         5379    .ARM.Collect$$rtexit$$00000003  c_w.l(rtexit2.o)
    0x0800030c   0x0800030c   0x00000006   Code   RO         5380    .ARM.Collect$$rtexit$$00000004  c_w.l(rtexit2.o)
    0x08000312   0x08000312   0x00000002   PAD
    0x08000314   0x08000314   0x00000040   Code   RO            4    .text               startup_stm32f407xx.o
    0x08000354   0x08000354   0x000000ee   Code   RO         4999    .text               c_w.l(lludivv7m.o)
    0x08000442   0x08000442   0x00000002   PAD
    0x08000444   0x08000444   0x00000034   Code   RO         5001    .text               c_w.l(vsnprintf.o)
    0x08000478   0x08000478   0x0000003c   Code   RO         5003    .text               c_w.l(__0sscanf.o)
    0x080004b4   0x080004b4   0x0000014c   Code   RO         5005    .text               c_w.l(_scanf_int.o)
    0x08000600   0x08000600   0x00000080   Code   RO         5007    .text               c_w.l(assert.o)
    0x08000680   0x08000680   0x0000008a   Code   RO         5009    .text               c_w.l(rt_memcpy_v6.o)
    0x0800070a   0x0800070a   0x0000004e   Code   RO         5011    .text               c_w.l(rt_memclr_w.o)
    0x08000758   0x08000758   0x00000006   Code   RO         5013    .text               c_w.l(heapauxi.o)
    0x0800075e   0x0800075e   0x00000016   Code   RO         5058    .text               c_w.l(_rserrno.o)
    0x08000774   0x08000774   0x0000004e   Code   RO         5062    .text               c_w.l(_printf_pad.o)
    0x080007c2   0x080007c2   0x00000024   Code   RO         5064    .text               c_w.l(_printf_truncate.o)
    0x080007e6   0x080007e6   0x00000052   Code   RO         5066    .text               c_w.l(_printf_str.o)
    0x08000838   0x08000838   0x00000078   Code   RO         5068    .text               c_w.l(_printf_dec.o)
    0x080008b0   0x080008b0   0x00000028   Code   RO         5070    .text               c_w.l(_printf_charcount.o)
    0x080008d8   0x080008d8   0x00000030   Code   RO         5072    .text               c_w.l(_printf_char_common.o)
    0x08000908   0x08000908   0x0000000a   Code   RO         5074    .text               c_w.l(_sputc.o)
    0x08000912   0x08000912   0x00000010   Code   RO         5076    .text               c_w.l(_snputc.o)
    0x08000922   0x08000922   0x00000002   PAD
    0x08000924   0x08000924   0x000000bc   Code   RO         5078    .text               c_w.l(_printf_wctomb.o)
    0x080009e0   0x080009e0   0x0000007c   Code   RO         5081    .text               c_w.l(_printf_longlong_dec.o)
    0x08000a5c   0x08000a5c   0x00000070   Code   RO         5087    .text               c_w.l(_printf_oct_int_ll.o)
    0x08000acc   0x08000acc   0x00000094   Code   RO         5107    .text               c_w.l(_printf_hex_int_ll_ptr.o)
    0x08000b60   0x08000b60   0x00000188   Code   RO         5127    .text               c_w.l(__printf_flags_ss_wp.o)
    0x08000ce8   0x08000ce8   0x0000001c   Code   RO         5153    .text               c_w.l(_chval.o)
    0x08000d04   0x08000d04   0x0000002c   Code   RO         5155    .text               c_w.l(scanf_char.o)
    0x08000d30   0x08000d30   0x00000040   Code   RO         5157    .text               c_w.l(_sgetc.o)
    0x08000d70   0x08000d70   0x00000016   Code   RO         5159    .text               c_w.l(abort.o)
    0x08000d86   0x08000d86   0x00000014   Code   RO         5161    .text               c_w.l(assert_puts.o)
    0x08000d9a   0x08000d9a   0x00000064   Code   RO         5163    .text               c_w.l(rt_memcpy_w.o)
    0x08000dfe   0x08000dfe   0x0000000e   Code   RO         5200    .text               c_w.l(sys_wrch.o)
    0x08000e0c   0x08000e0c   0x0000000c   Code   RO         5202    .text               c_w.l(sys_exit.o)
    0x08000e18   0x08000e18   0x00000008   Code   RO         5224    .text               c_w.l(rt_errno_addr_intlibspace.o)
    0x08000e20   0x08000e20   0x0000008a   Code   RO         5226    .text               c_w.l(lludiv10.o)
    0x08000eaa   0x08000eaa   0x00000012   Code   RO         5228    .text               c_w.l(isspace.o)
    0x08000ebc   0x08000ebc   0x000000b2   Code   RO         5230    .text               c_w.l(_printf_intcommon.o)
    0x08000f6e   0x08000f6e   0x0000041e   Code   RO         5232    .text               c_w.l(_printf_fp_dec.o)
    0x0800138c   0x0800138c   0x000002fc   Code   RO         5234    .text               c_w.l(_printf_fp_hex.o)
    0x08001688   0x08001688   0x0000002c   Code   RO         5239    .text               c_w.l(_printf_char.o)
    0x080016b4   0x080016b4   0x0000002c   Code   RO         5241    .text               c_w.l(_printf_wchar.o)
    0x080016e0   0x080016e0   0x00000374   Code   RO         5244    .text               c_w.l(_scanf.o)
    0x08001a54   0x08001a54   0x00000040   Code   RO         5246    .text               c_w.l(_wcrtomb.o)
    0x08001a94   0x08001a94   0x0000000e   Code   RO         5248    .text               c_w.l(defsig_abrt_outer.o)
    0x08001aa2   0x08001aa2   0x00000002   PAD
    0x08001aa4   0x08001aa4   0x00000008   Code   RO         5252    .text               c_w.l(libspace.o)
    0x08001aac   0x08001aac   0x00000002   Code   RO         5255    .text               c_w.l(use_no_semi.o)
    0x08001aae   0x08001aae   0x00000000   Code   RO         5257    .text               c_w.l(indicate_semi.o)
    0x08001aae   0x08001aae   0x0000004a   Code   RO         5258    .text               c_w.l(sys_stackheap_outer.o)
    0x08001af8   0x08001af8   0x00000010   Code   RO         5260    .text               c_w.l(rt_ctype_table.o)
    0x08001b08   0x08001b08   0x00000008   Code   RO         5267    .text               c_w.l(rt_locale_intlibspace.o)
    0x08001b10   0x08001b10   0x00000080   Code   RO         5269    .text               c_w.l(_printf_fp_infnan.o)
    0x08001b90   0x08001b90   0x000000e4   Code   RO         5271    .text               c_w.l(bigflt0.o)
    0x08001c74   0x08001c74   0x00000012   Code   RO         5299    .text               c_w.l(exit.o)
    0x08001c86   0x08001c86   0x0000000a   Code   RO         5301    .text               c_w.l(defsig_exit.o)
    0x08001c90   0x08001c90   0x00000030   Code   RO         5303    .text               c_w.l(defsig_abrt_inner.o)
    0x08001cc0   0x08001cc0   0x00000032   Code   RO         5326    .text               c_w.l(defsig_general.o)
    0x08001cf2   0x08001cf2   0x00000002   PAD
    0x08001cf4   0x08001cf4   0x00000080   Code   RO         5330    .text               c_w.l(strcmpv7m.o)
    0x08001d74   0x08001d74   0x0000003e   Code   RO         5274    CL$$btod_d2e        c_w.l(btod.o)
    0x08001db2   0x08001db2   0x00000046   Code   RO         5276    CL$$btod_d2e_denorm_low  c_w.l(btod.o)
    0x08001df8   0x08001df8   0x00000060   Code   RO         5275    CL$$btod_d2e_norm_op1  c_w.l(btod.o)
    0x08001e58   0x08001e58   0x00000338   Code   RO         5284    CL$$btod_div_common  c_w.l(btod.o)
    0x08002190   0x08002190   0x000000dc   Code   RO         5281    CL$$btod_e2e        c_w.l(btod.o)
    0x0800226c   0x0800226c   0x0000002a   Code   RO         5278    CL$$btod_ediv       c_w.l(btod.o)
    0x08002296   0x08002296   0x0000002a   Code   RO         5277    CL$$btod_emul       c_w.l(btod.o)
    0x080022c0   0x080022c0   0x00000244   Code   RO         5283    CL$$btod_mult_common  c_w.l(btod.o)
    0x08002504   0x08002504   0x00000094   Code   RO         4666    i.Angle_PID_control  pid_app.o
    0x08002598   0x08002598   0x00000004   Code   RO          474    i.BusFault_Handler  stm32f4xx_it.o
    0x0800259c   0x0800259c   0x00000178   Code   RO         4957    i.Car_State_Update  my_timer.o
    0x08002714   0x08002714   0x00000010   Code   RO          475    i.DMA1_Stream0_IRQHandler  stm32f4xx_it.o
    0x08002724   0x08002724   0x00000010   Code   RO          476    i.DMA2_Stream2_IRQHandler  stm32f4xx_it.o
    0x08002734   0x08002734   0x00000034   Code   RO         1609    i.DMA_CalcBaseAndBitshift  stm32f4xx_hal_dma.o
    0x08002768   0x08002768   0x000000aa   Code   RO         1610    i.DMA_CheckFifoParam  stm32f4xx_hal_dma.o
    0x08002812   0x08002812   0x0000002c   Code   RO         1611    i.DMA_SetConfig     stm32f4xx_hal_dma.o
    0x0800283e   0x0800283e   0x000000e0   Code   RO         3999    i.DRV8871_Control   motor_driver.o
    0x0800291e   0x0800291e   0x00000002   Code   RO          477    i.DebugMon_Handler  stm32f4xx_it.o
    0x08002920   0x08002920   0x00000058   Code   RO         3784    i.Delay_us          software_iic.o
    0x08002978   0x08002978   0x0000000a   Code   RO          478    i.EXTI3_IRQHandler  stm32f4xx_it.o
    0x08002982   0x08002982   0x00000002   PAD
    0x08002984   0x08002984   0x00000030   Code   RO         4209    i.Encoder_Driver_Init  encoder_driver.o
    0x080029b4   0x080029b4   0x00000090   Code   RO         4210    i.Encoder_Driver_Update  encoder_driver.o
    0x08002a44   0x08002a44   0x00000028   Code   RO         4626    i.Encoder_Init      encoder_app.o
    0x08002a6c   0x08002a6c   0x00000018   Code   RO         4627    i.Encoder_Task      encoder_app.o
    0x08002a84   0x08002a84   0x00000006   Code   RO           13    i.Error_Handler     main.o
    0x08002a8a   0x08002a8a   0x0000002a   Code   RO         4000    i.Float_To_Speed1000  motor_driver.o
    0x08002ab4   0x08002ab4   0x0000006c   Code   RO         4716    i.Gray_Init         gray_app.o
    0x08002b20   0x08002b20   0x0000007c   Code   RO         4717    i.Gray_Task         gray_app.o
    0x08002b9c   0x08002b9c   0x000000ac   Code   RO         1612    i.HAL_DMA_Abort     stm32f4xx_hal_dma.o
    0x08002c48   0x08002c48   0x00000028   Code   RO         1613    i.HAL_DMA_Abort_IT  stm32f4xx_hal_dma.o
    0x08002c70   0x08002c70   0x00000240   Code   RO         1617    i.HAL_DMA_IRQHandler  stm32f4xx_hal_dma.o
    0x08002eb0   0x08002eb0   0x000000ec   Code   RO         1618    i.HAL_DMA_Init      stm32f4xx_hal_dma.o
    0x08002f9c   0x08002f9c   0x00000092   Code   RO         1622    i.HAL_DMA_Start_IT  stm32f4xx_hal_dma.o
    0x0800302e   0x0800302e   0x00000002   PAD
    0x08003030   0x08003030   0x00000028   Code   RO         2054    i.HAL_Delay         stm32f4xx_hal.o
    0x08003058   0x08003058   0x00000002   Code   RO         1503    i.HAL_GPIO_EXTI_Callback  stm32f4xx_hal_gpio.o
    0x0800305a   0x0800305a   0x00000002   PAD
    0x0800305c   0x0800305c   0x0000001c   Code   RO         1504    i.HAL_GPIO_EXTI_IRQHandler  stm32f4xx_hal_gpio.o
    0x08003078   0x08003078   0x000001f4   Code   RO         1505    i.HAL_GPIO_Init     stm32f4xx_hal_gpio.o
    0x0800326c   0x0800326c   0x00000010   Code   RO         1507    i.HAL_GPIO_ReadPin  stm32f4xx_hal_gpio.o
    0x0800327c   0x0800327c   0x0000000c   Code   RO         1509    i.HAL_GPIO_WritePin  stm32f4xx_hal_gpio.o
    0x08003288   0x08003288   0x0000000c   Code   RO         2060    i.HAL_GetTick       stm32f4xx_hal.o
    0x08003294   0x08003294   0x000001d0   Code   RO          621    i.HAL_I2C_Init      stm32f4xx_hal_i2c.o
    0x08003464   0x08003464   0x00000308   Code   RO          639    i.HAL_I2C_Mem_Read  stm32f4xx_hal_i2c.o
    0x0800376c   0x0800376c   0x00000164   Code   RO          642    i.HAL_I2C_Mem_Write  stm32f4xx_hal_i2c.o
    0x080038d0   0x080038d0   0x000000f4   Code   RO          301    i.HAL_I2C_MspInit   i2c.o
    0x080039c4   0x080039c4   0x00000018   Code   RO         2066    i.HAL_IncTick       stm32f4xx_hal.o
    0x080039dc   0x080039dc   0x0000003c   Code   RO         2067    i.HAL_Init          stm32f4xx_hal.o
    0x08003a18   0x08003a18   0x0000004c   Code   RO         2068    i.HAL_InitTick      stm32f4xx_hal.o
    0x08003a64   0x08003a64   0x00000050   Code   RO          586    i.HAL_MspInit       stm32f4xx_hal_msp.o
    0x08003ab4   0x08003ab4   0x00000028   Code   RO         1897    i.HAL_NVIC_EnableIRQ  stm32f4xx_hal_cortex.o
    0x08003adc   0x08003adc   0x0000007c   Code   RO         1903    i.HAL_NVIC_SetPriority  stm32f4xx_hal_cortex.o
    0x08003b58   0x08003b58   0x00000028   Code   RO         1904    i.HAL_NVIC_SetPriorityGrouping  stm32f4xx_hal_cortex.o
    0x08003b80   0x08003b80   0x00000184   Code   RO         1106    i.HAL_RCC_ClockConfig  stm32f4xx_hal_rcc.o
    0x08003d04   0x08003d04   0x0000000c   Code   RO         1111    i.HAL_RCC_GetHCLKFreq  stm32f4xx_hal_rcc.o
    0x08003d10   0x08003d10   0x00000020   Code   RO         1113    i.HAL_RCC_GetPCLK1Freq  stm32f4xx_hal_rcc.o
    0x08003d30   0x08003d30   0x00000020   Code   RO         1114    i.HAL_RCC_GetPCLK2Freq  stm32f4xx_hal_rcc.o
    0x08003d50   0x08003d50   0x000000b0   Code   RO         1115    i.HAL_RCC_GetSysClockFreq  stm32f4xx_hal_rcc.o
    0x08003e00   0x08003e00   0x0000049c   Code   RO         1118    i.HAL_RCC_OscConfig  stm32f4xx_hal_rcc.o
    0x0800429c   0x0800429c   0x00000034   Code   RO         1908    i.HAL_SYSTICK_Config  stm32f4xx_hal_cortex.o
    0x080042d0   0x080042d0   0x00000002   Code   RO         3022    i.HAL_TIMEx_BreakCallback  stm32f4xx_hal_tim_ex.o
    0x080042d2   0x080042d2   0x00000002   Code   RO         3023    i.HAL_TIMEx_CommutCallback  stm32f4xx_hal_tim_ex.o
    0x080042d4   0x080042d4   0x00000074   Code   RO         3025    i.HAL_TIMEx_ConfigBreakDeadTime  stm32f4xx_hal_tim_ex.o
    0x08004348   0x08004348   0x000000b4   Code   RO         3041    i.HAL_TIMEx_MasterConfigSynchronization  stm32f4xx_hal_tim_ex.o
    0x080043fc   0x080043fc   0x00000066   Code   RO         2307    i.HAL_TIM_Base_Init  stm32f4xx_hal_tim.o
    0x08004462   0x08004462   0x00000002   PAD
    0x08004464   0x08004464   0x00000074   Code   RO          349    i.HAL_TIM_Base_MspInit  tim.o
    0x080044d8   0x080044d8   0x000000a8   Code   RO         2312    i.HAL_TIM_Base_Start_IT  stm32f4xx_hal_tim.o
    0x08004580   0x08004580   0x0000010c   Code   RO         2316    i.HAL_TIM_ConfigClockSource  stm32f4xx_hal_tim.o
    0x0800468c   0x0800468c   0x000000c8   Code   RO         2328    i.HAL_TIM_Encoder_Init  stm32f4xx_hal_tim.o
    0x08004754   0x08004754   0x000000f8   Code   RO          351    i.HAL_TIM_Encoder_MspInit  tim.o
    0x0800484c   0x0800484c   0x000000cc   Code   RO         2331    i.HAL_TIM_Encoder_Start  stm32f4xx_hal_tim.o
    0x08004918   0x08004918   0x00000002   Code   RO         2341    i.HAL_TIM_IC_CaptureCallback  stm32f4xx_hal_tim.o
    0x0800491a   0x0800491a   0x0000016c   Code   RO         2355    i.HAL_TIM_IRQHandler  stm32f4xx_hal_tim.o
    0x08004a86   0x08004a86   0x00000002   PAD
    0x08004a88   0x08004a88   0x00000064   Code   RO          352    i.HAL_TIM_MspPostInit  tim.o
    0x08004aec   0x08004aec   0x00000002   Code   RO         2358    i.HAL_TIM_OC_DelayElapsedCallback  stm32f4xx_hal_tim.o
    0x08004aee   0x08004aee   0x00000104   Code   RO         2379    i.HAL_TIM_PWM_ConfigChannel  stm32f4xx_hal_tim.o
    0x08004bf2   0x08004bf2   0x00000066   Code   RO         2382    i.HAL_TIM_PWM_Init  stm32f4xx_hal_tim.o
    0x08004c58   0x08004c58   0x00000002   Code   RO         2384    i.HAL_TIM_PWM_MspInit  stm32f4xx_hal_tim.o
    0x08004c5a   0x08004c5a   0x00000002   Code   RO         2385    i.HAL_TIM_PWM_PulseFinishedCallback  stm32f4xx_hal_tim.o
    0x08004c5c   0x08004c5c   0x0000010c   Code   RO         2387    i.HAL_TIM_PWM_Start  stm32f4xx_hal_tim.o
    0x08004d68   0x08004d68   0x00000144   Code   RO         4958    i.HAL_TIM_PeriodElapsedCallback  my_timer.o
    0x08004eac   0x08004eac   0x00000002   Code   RO         2398    i.HAL_TIM_TriggerCallback  stm32f4xx_hal_tim.o
    0x08004eae   0x08004eae   0x00000070   Code   RO         3299    i.HAL_UARTEx_ReceiveToIdle_DMA  stm32f4xx_hal_uart.o
    0x08004f1e   0x08004f1e   0x00000002   PAD
    0x08004f20   0x08004f20   0x00000058   Code   RO         4406    i.HAL_UARTEx_RxEventCallback  usart_app.o
    0x08004f78   0x08004f78   0x0000008a   Code   RO         3313    i.HAL_UART_DMAStop  stm32f4xx_hal_uart.o
    0x08005002   0x08005002   0x00000002   Code   RO         3315    i.HAL_UART_ErrorCallback  stm32f4xx_hal_uart.o
    0x08005004   0x08005004   0x00000308   Code   RO         3318    i.HAL_UART_IRQHandler  stm32f4xx_hal_uart.o
    0x0800530c   0x0800530c   0x00000076   Code   RO         3319    i.HAL_UART_Init     stm32f4xx_hal_uart.o
    0x08005382   0x08005382   0x00000002   PAD
    0x08005384   0x08005384   0x000001e0   Code   RO          427    i.HAL_UART_MspInit  usart.o
    0x08005564   0x08005564   0x00000002   Code   RO         3325    i.HAL_UART_RxCpltCallback  stm32f4xx_hal_uart.o
    0x08005566   0x08005566   0x00000002   Code   RO         3326    i.HAL_UART_RxHalfCpltCallback  stm32f4xx_hal_uart.o
    0x08005568   0x08005568   0x000000be   Code   RO         3327    i.HAL_UART_Transmit  stm32f4xx_hal_uart.o
    0x08005626   0x08005626   0x00000002   Code   RO         3330    i.HAL_UART_TxCpltCallback  stm32f4xx_hal_uart.o
    0x08005628   0x08005628   0x00000004   Code   RO          479    i.HardFault_Handler  stm32f4xx_it.o
    0x0800562c   0x0800562c   0x0000003e   Code   RO          665    i.I2C_IsAcknowledgeFailed  stm32f4xx_hal_i2c.o
    0x0800566a   0x0800566a   0x00000002   PAD
    0x0800566c   0x0800566c   0x00000160   Code   RO          676    i.I2C_RequestMemoryRead  stm32f4xx_hal_i2c.o
    0x080057cc   0x080057cc   0x000000e0   Code   RO          677    i.I2C_RequestMemoryWrite  stm32f4xx_hal_i2c.o
    0x080058ac   0x080058ac   0x00000066   Code   RO          685    i.I2C_WaitOnBTFFlagUntilTimeout  stm32f4xx_hal_i2c.o
    0x08005912   0x08005912   0x000000be   Code   RO          686    i.I2C_WaitOnFlagUntilTimeout  stm32f4xx_hal_i2c.o
    0x080059d0   0x080059d0   0x000000fa   Code   RO          687    i.I2C_WaitOnMasterAddressFlagUntilTimeout  stm32f4xx_hal_i2c.o
    0x08005aca   0x08005aca   0x0000008a   Code   RO          688    i.I2C_WaitOnRXNEFlagUntilTimeout  stm32f4xx_hal_i2c.o
    0x08005b54   0x08005b54   0x00000066   Code   RO          691    i.I2C_WaitOnTXEFlagUntilTimeout  stm32f4xx_hal_i2c.o
    0x08005bba   0x08005bba   0x0000000a   Code   RO         3786    i.IIC_Delay         software_iic.o
    0x08005bc4   0x08005bc4   0x00000020   Code   RO         3788    i.IIC_Get_Digtal    software_iic.o
    0x08005be4   0x08005be4   0x00000076   Code   RO         3792    i.IIC_ReadBytes     software_iic.o
    0x08005c5a   0x08005c5a   0x00000002   PAD
    0x08005c5c   0x08005c5c   0x0000007c   Code   RO         3793    i.IIC_RecvByte      software_iic.o
    0x08005cd8   0x08005cd8   0x00000034   Code   RO         3794    i.IIC_SendAck       software_iic.o
    0x08005d0c   0x08005d0c   0x00000058   Code   RO         3795    i.IIC_SendByte      software_iic.o
    0x08005d64   0x08005d64   0x0000002c   Code   RO         3796    i.IIC_SendNAck      software_iic.o
    0x08005d90   0x08005d90   0x00000038   Code   RO         3797    i.IIC_Start         software_iic.o
    0x08005dc8   0x08005dc8   0x00000034   Code   RO         3798    i.IIC_Stop          software_iic.o
    0x08005dfc   0x08005dfc   0x0000003c   Code   RO         3799    i.IIC_WaitAck       software_iic.o
    0x08005e38   0x08005e38   0x00000088   Code   RO         4667    i.Line_PID_control  pid_app.o
    0x08005ec0   0x08005ec0   0x00000130   Code   RO         4254    i.MPU6050_CalculateEuler  mpu6050_hal.o
    0x08005ff0   0x08005ff0   0x000000f0   Code   RO         4255    i.MPU6050_CalibrateGyro  mpu6050_hal.o
    0x080060e0   0x080060e0   0x0000006c   Code   RO         4256    i.MPU6050_Config    mpu6050_hal.o
    0x0800614c   0x0800614c   0x00000088   Code   RO         4258    i.MPU6050_Init      mpu6050_hal.o
    0x080061d4   0x080061d4   0x00000118   Code   RO         4259    i.MPU6050_ReadData  mpu6050_hal.o
    0x080062ec   0x080062ec   0x00000094   Code   RO         4260    i.MPU6050_ReadRawData  mpu6050_hal.o
    0x08006380   0x08006380   0x00000038   Code   RO         4261    i.MPU6050_ReadReg   mpu6050_hal.o
    0x080063b8   0x080063b8   0x00000038   Code   RO         4262    i.MPU6050_ReadRegs  mpu6050_hal.o
    0x080063f0   0x080063f0   0x0000000c   Code   RO         4263    i.MPU6050_Reset     mpu6050_hal.o
    0x080063fc   0x080063fc   0x00000024   Code   RO         4264    i.MPU6050_TestConnection  mpu6050_hal.o
    0x08006420   0x08006420   0x000000d4   Code   RO         4265    i.MPU6050_UpdateScales  mpu6050_hal.o
    0x080064f4   0x080064f4   0x0000003c   Code   RO         4266    i.MPU6050_WriteReg  mpu6050_hal.o
    0x08006530   0x08006530   0x00000068   Code   RO          276    i.MX_DMA_Init       dma.o
    0x08006598   0x08006598   0x000001cc   Code   RO          252    i.MX_GPIO_Init      gpio.o
    0x08006764   0x08006764   0x0000003c   Code   RO          302    i.MX_I2C1_Init      i2c.o
    0x080067a0   0x080067a0   0x0000003c   Code   RO          303    i.MX_I2C2_Init      i2c.o
    0x080067dc   0x080067dc   0x00000108   Code   RO          353    i.MX_TIM1_Init      tim.o
    0x080068e4   0x080068e4   0x0000006c   Code   RO          354    i.MX_TIM2_Init      tim.o
    0x08006950   0x08006950   0x00000080   Code   RO          355    i.MX_TIM3_Init      tim.o
    0x080069d0   0x080069d0   0x00000080   Code   RO          356    i.MX_TIM4_Init      tim.o
    0x08006a50   0x08006a50   0x00000038   Code   RO          428    i.MX_UART5_Init     usart.o
    0x08006a88   0x08006a88   0x00000038   Code   RO          429    i.MX_USART1_UART_Init  usart.o
    0x08006ac0   0x08006ac0   0x00000004   Code   RO          480    i.MemManage_Handler  stm32f4xx_it.o
    0x08006ac4   0x08006ac4   0x000000c4   Code   RO         4001    i.Motor_Create      motor_driver.o
    0x08006b88   0x08006b88   0x00000060   Code   RO         4578    i.Motor_Init        motor_app.o
    0x08006be8   0x08006be8   0x000000a0   Code   RO         4005    i.Motor_SetSpeed    motor_driver.o
    0x08006c88   0x08006c88   0x00000030   Code   RO         4007    i.Motor_ValidateFloatSpeed  motor_driver.o
    0x08006cb8   0x08006cb8   0x0000001a   Code   RO         4008    i.Motor_ValidateParams  motor_driver.o
    0x08006cd2   0x08006cd2   0x00000004   Code   RO          481    i.NMI_Handler       stm32f4xx_it.o
    0x08006cd6   0x08006cd6   0x00000038   Code   RO         4087    i.OLED_Clear        oled.o
    0x08006d0e   0x08006d0e   0x00000002   PAD
    0x08006d10   0x08006d10   0x00000030   Code   RO         4090    i.OLED_Init         oled.o
    0x08006d40   0x08006d40   0x00000024   Code   RO         4092    i.OLED_Set_Position  oled.o
    0x08006d64   0x08006d64   0x0000009c   Code   RO         4093    i.OLED_ShowChar     oled.o
    0x08006e00   0x08006e00   0x0000003a   Code   RO         4099    i.OLED_ShowStr      oled.o
    0x08006e3a   0x08006e3a   0x00000002   PAD
    0x08006e3c   0x08006e3c   0x00000024   Code   RO         4100    i.OLED_Write_cmd    oled.o
    0x08006e60   0x08006e60   0x00000024   Code   RO         4101    i.OLED_Write_data   oled.o
    0x08006e84   0x08006e84   0x00000110   Code   RO         4668    i.PID_Init          pid_app.o
    0x08006f94   0x08006f94   0x00000154   Code   RO         4669    i.PID_Task          pid_app.o
    0x080070e8   0x080070e8   0x00000002   Code   RO          482    i.PendSV_Handler    stm32f4xx_it.o
    0x080070ea   0x080070ea   0x0000001e   Code   RO         3802    i.Ping              software_iic.o
    0x08007108   0x08007108   0x00000002   Code   RO          483    i.SVC_Handler       stm32f4xx_it.o
    0x0800710a   0x0800710a   0x00000066   Code   RO         4009    i.Set_Pin_Mode      motor_driver.o
    0x08007170   0x08007170   0x00000058   Code   RO         4010    i.Speed1000_To_PWM  motor_driver.o
    0x080071c8   0x080071c8   0x00000008   Code   RO          484    i.SysTick_Handler   stm32f4xx_it.o
    0x080071d0   0x080071d0   0x000000b4   Code   RO           14    i.SystemClock_Config  main.o
    0x08007284   0x08007284   0x00000014   Code   RO         3663    i.SystemInit        system_stm32f4xx.o
    0x08007298   0x08007298   0x00000010   Code   RO          485    i.TIM2_IRQHandler   stm32f4xx_it.o
    0x080072a8   0x080072a8   0x000000e0   Code   RO         2400    i.TIM_Base_SetConfig  stm32f4xx_hal_tim.o
    0x08007388   0x08007388   0x00000022   Code   RO         2401    i.TIM_CCxChannelCmd  stm32f4xx_hal_tim.o
    0x080073aa   0x080073aa   0x00000016   Code   RO         2411    i.TIM_ETR_SetConfig  stm32f4xx_hal_tim.o
    0x080073c0   0x080073c0   0x00000012   Code   RO         2412    i.TIM_ITRx_SetConfig  stm32f4xx_hal_tim.o
    0x080073d2   0x080073d2   0x00000002   PAD
    0x080073d4   0x080073d4   0x00000070   Code   RO         2413    i.TIM_OC1_SetConfig  stm32f4xx_hal_tim.o
    0x08007444   0x08007444   0x0000007c   Code   RO         2414    i.TIM_OC2_SetConfig  stm32f4xx_hal_tim.o
    0x080074c0   0x080074c0   0x00000078   Code   RO         2415    i.TIM_OC3_SetConfig  stm32f4xx_hal_tim.o
    0x08007538   0x08007538   0x00000054   Code   RO         2416    i.TIM_OC4_SetConfig  stm32f4xx_hal_tim.o
    0x0800758c   0x0800758c   0x00000026   Code   RO         2418    i.TIM_TI1_ConfigInputStage  stm32f4xx_hal_tim.o
    0x080075b2   0x080075b2   0x00000028   Code   RO         2420    i.TIM_TI2_ConfigInputStage  stm32f4xx_hal_tim.o
    0x080075da   0x080075da   0x00000002   PAD
    0x080075dc   0x080075dc   0x00000010   Code   RO          486    i.UART5_IRQHandler  stm32f4xx_it.o
    0x080075ec   0x080075ec   0x00000012   Code   RO         3332    i.UART_DMAAbortOnError  stm32f4xx_hal_uart.o
    0x080075fe   0x080075fe   0x00000050   Code   RO         3333    i.UART_DMAError     stm32f4xx_hal_uart.o
    0x0800764e   0x0800764e   0x000000b4   Code   RO         3334    i.UART_DMAReceiveCplt  stm32f4xx_hal_uart.o
    0x08007702   0x08007702   0x00000024   Code   RO         3336    i.UART_DMARxHalfCplt  stm32f4xx_hal_uart.o
    0x08007726   0x08007726   0x0000006c   Code   RO         3342    i.UART_EndRxTransfer  stm32f4xx_hal_uart.o
    0x08007792   0x08007792   0x00000020   Code   RO         3343    i.UART_EndTransmit_IT  stm32f4xx_hal_uart.o
    0x080077b2   0x080077b2   0x00000026   Code   RO         3344    i.UART_EndTxTransfer  stm32f4xx_hal_uart.o
    0x080077d8   0x080077d8   0x000000fc   Code   RO         3345    i.UART_Receive_IT   stm32f4xx_hal_uart.o
    0x080078d4   0x080078d4   0x0000022c   Code   RO         3346    i.UART_SetConfig    stm32f4xx_hal_uart.o
    0x08007b00   0x08007b00   0x000000d8   Code   RO         3347    i.UART_Start_Receive_DMA  stm32f4xx_hal_uart.o
    0x08007bd8   0x08007bd8   0x00000060   Code   RO         3349    i.UART_Transmit_IT  stm32f4xx_hal_uart.o
    0x08007c38   0x08007c38   0x0000008c   Code   RO         3350    i.UART_WaitOnFlagUntilTimeout  stm32f4xx_hal_uart.o
    0x08007cc4   0x08007cc4   0x00000010   Code   RO          487    i.USART1_IRQHandler  stm32f4xx_it.o
    0x08007cd4   0x08007cd4   0x00000004   Code   RO          488    i.UsageFault_Handler  stm32f4xx_it.o
    0x08007cd8   0x08007cd8   0x00000030   Code   RO         5317    i.__ARM_fpclassify  m_wm.l(fpclassify.o)
    0x08007d08   0x08007d08   0x00000026   Code   RO         5184    i.__ARM_fpclassifyf  m_wm.l(fpclassifyf.o)
    0x08007d2e   0x08007d2e   0x00000002   PAD
    0x08007d30   0x08007d30   0x00000010   Code   RO         1910    i.__NVIC_GetPriorityGrouping  stm32f4xx_hal_cortex.o
    0x08007d40   0x08007d40   0x00000028   Code   RO         1911    i.__NVIC_SetPriority  stm32f4xx_hal_cortex.o
    0x08007d68   0x08007d68   0x000002ac   Code   RO         5025    i.__hardfp_atan2f   m_wm.l(atan2f.o)
    0x08008014   0x08008014   0x0000009a   Code   RO         5049    i.__hardfp_roundf   m_wm.l(roundf.o)
    0x080080ae   0x080080ae   0x0000003a   Code   RO         5037    i.__hardfp_sqrtf    m_wm.l(sqrtf.o)
    0x080080e8   0x080080e8   0x00000006   Code   RO         5188    i.__mathlib_flt_infnan2  m_wm.l(funder.o)
    0x080080ee   0x080080ee   0x00000002   PAD
    0x080080f0   0x080080f0   0x00000010   Code   RO         5192    i.__mathlib_flt_underflow  m_wm.l(funder.o)
    0x08008100   0x08008100   0x0000000e   Code   RO         5120    i._is_digit         c_w.l(__printf_wp.o)
    0x0800810e   0x0800810e   0x00000002   PAD
    0x08008110   0x08008110   0x00000034   Code   RO         4885    i.all_task_init     my_scheduler.o
    0x08008144   0x08008144   0x00000058   Code   RO         4886    i.all_task_run      my_scheduler.o
    0x0800819c   0x0800819c   0x000000a0   Code   RO         4759    i.bno080_task       mpu6050_app.o
    0x0800823c   0x0800823c   0x00000098   Code   RO         4761    i.convert_to_continuous_yaw  mpu6050_app.o
    0x080082d4   0x080082d4   0x0000001c   Code   RO         4772    i.get_yaw           mpu6050_app.o
    0x080082f0   0x080082f0   0x0000004c   Code   RO         4379    i.key_read          key_driver.o
    0x0800833c   0x0800833c   0x0000008c   Code   RO         4505    i.key_task          key_app.o
    0x080083c8   0x080083c8   0x00000084   Code   RO         4351    i.led_disp          led_driver.o
    0x0800844c   0x0800844c   0x00000002   Code   RO         4469    i.led_init          led_app.o
    0x0800844e   0x0800844e   0x00000002   PAD
    0x08008450   0x08008450   0x00000010   Code   RO         4470    i.led_task          led_app.o
    0x08008460   0x08008460   0x0000003c   Code   RO           15    i.main              main.o
    0x0800849c   0x0800849c   0x00000020   Code   RO         4580    i.motor_set_l       motor_app.o
    0x080084bc   0x080084bc   0x00000020   Code   RO         4581    i.motor_set_r       motor_app.o
    0x080084dc   0x080084dc   0x000001f0   Code   RO         4773    i.my_bno080_init    mpu6050_app.o
    0x080086cc   0x080086cc   0x00000008   Code   RO         4536    i.my_oled_init      oled_app.o
    0x080086d4   0x080086d4   0x0000003a   Code   RO         4407    i.my_printf         usart_app.o
    0x0800870e   0x0800870e   0x0000003c   Code   RO         4537    i.oled_printf       oled_app.o
    0x0800874a   0x0800874a   0x00000002   PAD
    0x0800874c   0x0800874c   0x000000c4   Code   RO         4538    i.oled_task         oled_app.o
    0x08008810   0x08008810   0x00000026   Code   RO         3920    i.pid_calculate_positional  pid.o
    0x08008836   0x08008836   0x0000002a   Code   RO         3921    i.pid_constrain     pid.o
    0x08008860   0x08008860   0x0000007a   Code   RO         3923    i.pid_formula_positional  pid.o
    0x080088da   0x080088da   0x00000002   PAD
    0x080088dc   0x080088dc   0x00000054   Code   RO         3924    i.pid_init          pid.o
    0x08008930   0x08008930   0x00000040   Code   RO         3925    i.pid_out_limit     pid.o
    0x08008970   0x08008970   0x00000040   Code   RO         3926    i.pid_reset         pid.o
    0x080089b0   0x080089b0   0x00000006   Code   RO         3929    i.pid_set_target    pid.o
    0x080089b6   0x080089b6   0x0000003c   Code   RO         3696    i.rt_ringbuffer_data_len  ringbuffer.o
    0x080089f2   0x080089f2   0x00000002   PAD
    0x080089f4   0x080089f4   0x000000ec   Code   RO         3697    i.rt_ringbuffer_get  ringbuffer.o
    0x08008ae0   0x08008ae0   0x00000094   Code   RO         3699    i.rt_ringbuffer_init  ringbuffer.o
    0x08008b74   0x08008b74   0x000000f0   Code   RO         3701    i.rt_ringbuffer_put  ringbuffer.o
    0x08008c64   0x08008c64   0x0000002a   Code   RO         3706    i.rt_ringbuffer_status  ringbuffer.o
    0x08008c8e   0x08008c8e   0x00000002   PAD
    0x08008c90   0x08008c90   0x00000010   Code   RO         4959    i.timer_init        my_timer.o
    0x08008ca0   0x08008ca0   0x0000003c   Code   RO         4408    i.uart_init         usart_app.o
    0x08008cdc   0x08008cdc   0x000000f8   Code   RO         4409    i.uart_task         usart_app.o
    0x08008dd4   0x08008dd4   0x0000002c   Code   RO         5297    locale$$code        c_w.l(lc_numeric_c.o)
    0x08008e00   0x08008e00   0x0000002c   Code   RO         5322    locale$$code        c_w.l(lc_ctype_c.o)
    0x08008e2c   0x08008e2c   0x00000062   Code   RO         5017    x$fpl$d2f           fz_wm.l(d2f.o)
    0x08008e8e   0x08008e8e   0x00000002   PAD
    0x08008e90   0x08008e90   0x000002b0   Code   RO         5020    x$fpl$ddiv          fz_wm.l(ddiv.o)
    0x08009140   0x08009140   0x0000009c   Code   RO         5165    x$fpl$dnaninf       fz_wm.l(dnaninf.o)
    0x080091dc   0x080091dc   0x0000000c   Code   RO         5167    x$fpl$dretinf       fz_wm.l(dretinf.o)
    0x080091e8   0x080091e8   0x00000056   Code   RO         5023    x$fpl$f2d           fz_wm.l(f2d.o)
    0x0800923e   0x0800923e   0x0000008c   Code   RO         5169    x$fpl$fnaninf       fz_wm.l(fnaninf.o)
    0x080092ca   0x080092ca   0x0000000a   Code   RO         5388    x$fpl$fpinit        fz_wm.l(fpinit.o)
    0x080092d4   0x080092d4   0x0000000a   Code   RO         5171    x$fpl$fretinf       fz_wm.l(fretinf.o)
    0x080092de   0x080092de   0x00000002   PAD
    0x080092e0   0x080092e0   0x00000060   Code   RO         5173    x$fpl$frnd          fz_wm.l(frnd.o)
    0x08009340   0x08009340   0x00000004   Code   RO         5175    x$fpl$printf1       fz_wm.l(printf1.o)
    0x08009344   0x08009344   0x00000004   Code   RO         5177    x$fpl$printf2       fz_wm.l(printf2.o)
    0x08009348   0x08009348   0x00000000   Code   RO         5183    x$fpl$usenofp       fz_wm.l(usenofp.o)
    0x08009348   0x08009348   0x00000008   Data   RO         1624    .constdata          stm32f4xx_hal_dma.o
    0x08009350   0x08009350   0x00000018   Data   RO         3664    .constdata          system_stm32f4xx.o
    0x08009368   0x08009368   0x00000a98   Data   RO         4102    .constdata          oled.o
    0x08009e00   0x08009e00   0x00000004   Data   RO         4776    .constdata          mpu6050_app.o
    0x08009e04   0x08009e04   0x00000008   Data   RO         5079    .constdata          c_w.l(_printf_wctomb.o)
    0x08009e0c   0x08009e0c   0x00000028   Data   RO         5108    .constdata          c_w.l(_printf_hex_int_ll_ptr.o)
    0x08009e34   0x08009e34   0x00000011   Data   RO         5128    .constdata          c_w.l(__printf_flags_ss_wp.o)
    0x08009e45   0x08009e45   0x00000026   Data   RO         5235    .constdata          c_w.l(_printf_fp_hex.o)
    0x08009e6b   0x08009e6b   0x00000001   PAD
    0x08009e6c   0x08009e6c   0x00000094   Data   RO         5272    .constdata          c_w.l(bigflt0.o)
    0x08009f00   0x08009f00   0x00000020   Data   RO         5437    Region$$Table       anon$$obj.o
    0x08009f20   0x08009f20   0x0000001c   Data   RO         5296    locale$$data        c_w.l(lc_numeric_c.o)
    0x08009f3c   0x08009f3c   0x00000110   Data   RO         5321    locale$$data        c_w.l(lc_ctype_c.o)


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x0800a04c, Size: 0x00003800, Max: 0x0001c000, ABSOLUTE, COMPRESSED[0x000000a4])

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   COMPRESSED   0x00000009   Data   RW         2074    .data               stm32f4xx_hal.o
    0x20000009   COMPRESSED   0x00000003   PAD
    0x2000000c   COMPRESSED   0x00000004   Data   RW         3665    .data               system_stm32f4xx.o
    0x20000010   COMPRESSED   0x00000016   Data   RW         4103    .data               oled.o
    0x20000026   COMPRESSED   0x00000002   PAD
    0x20000028   COMPRESSED   0x0000002c   Data   RW         4267    .data               mpu6050_hal.o
    0x20000054   COMPRESSED   0x00000001   Data   RW         4352    .data               led_driver.o
    0x20000055   COMPRESSED   0x00000005   Data   RW         4471    .data               led_app.o
    0x2000005a   COMPRESSED   0x00000006   Data   RW         4506    .data               key_app.o
    0x20000060   COMPRESSED   0x00000058   Data   RW         4671    .data               pid_app.o
    0x200000b8   COMPRESSED   0x00000028   Data   RW         4718    .data               gray_app.o
    0x200000e0   COMPRESSED   0x00000025   Data   RW         4777    .data               mpu6050_app.o
    0x20000105   COMPRESSED   0x00000003   PAD
    0x20000108   COMPRESSED   0x0000003d   Data   RW         4887    .data               my_scheduler.o
    0x20000145   COMPRESSED   0x00000003   PAD
    0x20000148   COMPRESSED   0x0000001c   Data   RW         4960    .data               my_timer.o
    0x20000164        -       0x000000a8   Zero   RW          304    .bss                i2c.o
    0x2000020c        -       0x00000120   Zero   RW          357    .bss                tim.o
    0x2000032c        -       0x00000150   Zero   RW          430    .bss                usart.o
    0x2000047c        -       0x0000018c   Zero   RW         4410    .bss                usart_app.o
    0x20000608        -       0x00000060   Zero   RW         4582    .bss                motor_app.o
    0x20000668        -       0x00000020   Zero   RW         4628    .bss                encoder_app.o
    0x20000688        -       0x000000f0   Zero   RW         4670    .bss                pid_app.o
    0x20000778        -       0x00000028   Zero   RW         4775    .bss                mpu6050_app.o
    0x200007a0        -       0x00000060   Zero   RW         5253    .bss                c_w.l(libspace.o)
    0x20000800        -       0x00001000   Zero   RW            2    HEAP                startup_stm32f407xx.o
    0x20001800        -       0x00002000   Zero   RW            1    STACK               startup_stm32f407xx.o


    Execution Region RW_IRAM2 (Exec base: 0x2001c000, Load base: 0x0800a0f0, Size: 0x00000000, Max: 0x00004000, ABSOLUTE)

    **** No section assigned to this execution region ****


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       104          6          0          0          0        878   dma.o
        64         24          0          0         32       1963   encoder_app.o
       192         24          0          0          0       2527   encoder_driver.o
       460         20          0          0          0       1263   gpio.o
       232         96          0         40          0       2129   gray_app.o
       364         40          0          0        168       2466   i2c.o
       140         20          0          6          0        950   key_app.o
        76          8          0          0          0        595   key_driver.o
        18          6          0          5          0       1512   led_app.o
       132         12          0          1          0        971   led_driver.o
       246         10          0          0          0     702996   main.o
       160         28          0          0         96       2054   motor_app.o
       886         16          0          0          0       8272   motor_driver.o
       836        402          4         37         40       6406   mpu6050_app.o
      1648        164          0         44          0      13079   mpu6050_hal.o
       140         16          0         61          0       1871   my_scheduler.o
       716        106          0         28          0       3108   my_timer.o
       426         22       2712         22          0       5695   oled.o
       264         48          0          0          0       1909   oled_app.o
       420         12          0          0          0       5283   pid.o
       896        154          0         88        240       3845   pid_app.o
       726        178          0          0          0       6407   ringbuffer.o
       754         40          0          0          0       7847   software_iic.o
        64         26        392          0      12288        872   startup_stm32f407xx.o
       212         36          0          9          0       9661   stm32f4xx_hal.o
       312         22          0          0          0      34559   stm32f4xx_hal_cortex.o
      1436         16          8          0          0       7318   stm32f4xx_hal_dma.o
       558         50          0          0          0       4187   stm32f4xx_hal_gpio.o
      3016         48          0          0          0      12262   stm32f4xx_hal_i2c.o
        80          6          0          0          0        930   stm32f4xx_hal_msp.o
      1820         84          0          0          0       5864   stm32f4xx_hal_rcc.o
      2762        142          0          0          0      19403   stm32f4xx_hal_tim.o
       300         30          0          0          0       3513   stm32f4xx_hal_tim_ex.o
      3094         28          0          0          0      17844   stm32f4xx_hal_uart.o
       124         30          0          0          0       7706   stm32f4xx_it.o
        20          6         24          4          0       1223   system_stm32f4xx.o
      1092         78          0          0        288       5709   tim.o
       592         60          0          0        336       2762   usart.o
       454        122          0          0        396       6302   usart_app.o

    ----------------------------------------------------------------------
     25872       <USER>       <GROUP>        356      13884     924141   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        36          0          0         11          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

        60          8          0          0          0         84   __0sscanf.o
        90          0          0          0          0          0   __dczerorl2.o
         8          0          0          0          0         68   __main.o
       392          4         17          0          0         92   __printf_flags_ss_wp.o
        14          0          0          0          0         68   __printf_wp.o
         0          0          0          0          0          0   __rtentry.o
        12          0          0          0          0          0   __rtentry2.o
         6          0          0          0          0          0   __rtentry4.o
        52          8          0          0          0          0   __scatter.o
        28          0          0          0          0          0   __scatter_zi.o
        28          0          0          0          0         68   _chval.o
         6          0          0          0          0          0   _printf_a.o
         6          0          0          0          0          0   _printf_c.o
        44          0          0          0          0        108   _printf_char.o
        48          6          0          0          0         96   _printf_char_common.o
        40          0          0          0          0         68   _printf_charcount.o
         6          0          0          0          0          0   _printf_d.o
       120         16          0          0          0         92   _printf_dec.o
         6          0          0          0          0          0   _printf_e.o
         6          0          0          0          0          0   _printf_f.o
      1054          0          0          0          0        216   _printf_fp_dec.o
       764          8         38          0          0        100   _printf_fp_hex.o
       128         16          0          0          0         84   _printf_fp_infnan.o
         6          0          0          0          0          0   _printf_g.o
       148          4         40          0          0        160   _printf_hex_int_ll_ptr.o
         6          0          0          0          0          0   _printf_i.o
       178          0          0          0          0         88   _printf_intcommon.o
        10          0          0          0          0          0   _printf_l.o
         6          0          0          0          0          0   _printf_lc.o
        10          0          0          0          0          0   _printf_ll.o
         6          0          0          0          0          0   _printf_lld.o
         6          0          0          0          0          0   _printf_lli.o
         6          0          0          0          0          0   _printf_llo.o
         6          0          0          0          0          0   _printf_llu.o
         6          0          0          0          0          0   _printf_llx.o
       124         16          0          0          0         92   _printf_longlong_dec.o
         6          0          0          0          0          0   _printf_ls.o
         6          0          0          0          0          0   _printf_n.o
         6          0          0          0          0          0   _printf_o.o
       112         10          0          0          0        124   _printf_oct_int_ll.o
         6          0          0          0          0          0   _printf_p.o
        78          0          0          0          0        108   _printf_pad.o
         0          0          0          0          0          0   _printf_percent.o
         4          0          0          0          0          0   _printf_percent_end.o
         6          0          0          0          0          0   _printf_s.o
        82          0          0          0          0         80   _printf_str.o
        36          0          0          0          0         84   _printf_truncate.o
         6          0          0          0          0          0   _printf_u.o
        44          0          0          0          0        108   _printf_wchar.o
       188          6          8          0          0         92   _printf_wctomb.o
         6          0          0          0          0          0   _printf_x.o
        22          0          0          0          0        100   _rserrno.o
       884          4          0          0          0        100   _scanf.o
       332          0          0          0          0         96   _scanf_int.o
        64          0          0          0          0         84   _sgetc.o
        16          0          0          0          0         68   _snputc.o
        10          0          0          0          0         68   _sputc.o
        64          0          0          0          0         92   _wcrtomb.o
        22          0          0          0          0         80   abort.o
       128         42          0          0          0         76   assert.o
        20          0          0          0          0         76   assert_puts.o
       228          4        148          0          0         96   bigflt0.o
      1936        128          0          0          0        672   btod.o
        48         34          0          0          0         76   defsig_abrt_inner.o
        14          0          0          0          0         80   defsig_abrt_outer.o
        10          0          0          0          0         68   defsig_exit.o
        50          0          0          0          0         88   defsig_general.o
        18          0          0          0          0         80   exit.o
         6          0          0          0          0        152   heapauxi.o
         0          0          0          0          0          0   indicate_semi.o
        18          0          0          0          0         76   isspace.o
        44         10        272          0          0         76   lc_ctype_c.o
        44         10         28          0          0         76   lc_numeric_c.o
         2          0          0          0          0          0   libinit.o
        34          0          0          0          0          0   libinit2.o
         2          0          0          0          0          0   libshutdown.o
         2          0          0          0          0          0   libshutdown2.o
         8          4          0          0         96         68   libspace.o
       138          0          0          0          0         80   lludiv10.o
       238          0          0          0          0        100   lludivv7m.o
        16          4          0          0          0         76   rt_ctype_table.o
         8          4          0          0          0         68   rt_errno_addr_intlibspace.o
         8          4          0          0          0         68   rt_locale_intlibspace.o
        78          0          0          0          0         80   rt_memclr_w.o
       138          0          0          0          0         68   rt_memcpy_v6.o
       100          0          0          0          0         80   rt_memcpy_w.o
         2          0          0          0          0          0   rtexit.o
        10          0          0          0          0          0   rtexit2.o
        44          8          0          0          0         84   scanf_char.o
       128          0          0          0          0         68   strcmpv7m.o
        12          4          0          0          0         68   sys_exit.o
        74          0          0          0          0         80   sys_stackheap_outer.o
        14          0          0          0          0         76   sys_wrch.o
         2          0          0          0          0         68   use_no_semi.o
        52          4          0          0          0         80   vsnprintf.o
        98          4          0          0          0        140   d2f.o
       688        140          0          0          0        256   ddiv.o
       156          4          0          0          0        140   dnaninf.o
        12          0          0          0          0        116   dretinf.o
        86          4          0          0          0        132   f2d.o
       140          4          0          0          0        132   fnaninf.o
        10          0          0          0          0        116   fpinit.o
        10          0          0          0          0        116   fretinf.o
        96          4          0          0          0        124   frnd.o
         4          0          0          0          0        116   printf1.o
         4          0          0          0          0        116   printf2.o
         0          0          0          0          0          0   usenofp.o
       684         90          0          0          0        208   atan2f.o
        48          0          0          0          0        124   fpclassify.o
        38          0          0          0          0        116   fpclassifyf.o
        22          6          0          0          0        232   funder.o
       154          0          0          0          0        140   roundf.o
        58          0          0          0          0        136   sqrtf.o

    ----------------------------------------------------------------------
     11440        <USER>        <GROUP>          0         96       8132   Library Totals
        22          0          1          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      9110        366        551          0         96       5672   c_w.l
      1304        160          0          0          0       1504   fz_wm.l
      1004         96          0          0          0        956   m_wm.l

    ----------------------------------------------------------------------
     11440        <USER>        <GROUP>          0         96       8132   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     37312       2858       3724        356      13980     907449   Grand Totals
     37312       2858       3724        164      13980     907449   ELF Image Totals (compressed)
     37312       2858       3724        164          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                41036 (  40.07kB)
    Total RW  Size (RW Data + ZI Data)             14336 (  14.00kB)
    Total ROM Size (Code + RO Data + RW Data)      41200 (  40.23kB)

==============================================================================

